"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
// mqUtil.ts
var mq = require("ibmmq");
var ibmmq_1 = require("ibmmq");
// Configuration for connecting to the Queue Manager
var config = {
    qMgr: 'QM1', // Queue Manager name
    //queueName: 'DEV.TEST.QUEUE', // Queue name for testing
    queueName: 'DEV.QUEUE.1', // Queue name for testing
    host: 'localhost', // Set to null or remove for server bindings
    port: 1414,
    channel: 'DEV.APP.SVRCONN',
    user: '', // MQ user - populate if required by your MQ security
    password: '' // MQ password - populate if required
};
// Connect to the Queue Manager
function connectToQueueManager() {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    var cno = new mq.MQCNO();
                    if (config.host) {
                        cno.Options = ibmmq_1.MQC.MQCNO_CLIENT_BINDING; // Explicitly client
                        var csp = new mq.MQCSP();
                        if (config.user) { // Only set security params if user is provided
                            csp.UserId = config.user;
                            csp.Password = config.password;
                            cno.SecurityParms = csp;
                        }
                        var cd = new mq.MQCD();
                        cd.ConnectionName = "".concat(config.host, "(").concat(config.port, ")");
                        cd.ChannelName = config.channel;
                        cno.ClientConn = cd;
                    }
                    else {
                        cno.Options = ibmmq_1.MQC.MQCNO_STANDARD_BINDING; // Server binding
                    }
                    mq.Connx(config.qMgr, cno, function (err, hConn) {
                        if (err) {
                            reject(new Error("Connection failed: MQCC=".concat(err.mqcc, ", MQRC=").concat(err.mqrc, ", Message=").concat(err.message)));
                        }
                        else {
                            console.log('Connected to Queue Manager:', config.qMgr);
                            resolve({ hConn: hConn, queueManagerName: config.qMgr });
                        }
                    });
                })];
        });
    });
}
// Open a queue with specified options
function openQueue(hConn, queueName, openOptions) {
    return __awaiter(this, void 0, void 0, function () {
        return __generator(this, function (_a) {
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    var od = new mq.MQOD();
                    od.ObjectName = queueName;
                    od.ObjectType = ibmmq_1.MQC.MQOT_Q;
                    mq.Open(hConn, od, openOptions, function (err, hObj) {
                        if (err) {
                            reject(new Error("Open queue '".concat(queueName, "' failed: MQCC=").concat(err.mqcc, ", MQRC=").concat(err.mqrc, ", Message=").concat(err.message)));
                        }
                        else {
                            console.log("Opened queue: ".concat(queueName, " with handle ").concat(hObj));
                            resolve({ hObj: hObj, name: queueName });
                        }
                    });
                })];
        });
    });
}
// Close an MQ object
function closeObject(hObj_1) {
    return __awaiter(this, arguments, void 0, function (hObj, objectNameHint) {
        if (objectNameHint === void 0) { objectNameHint = "object"; }
        return __generator(this, function (_a) {
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    mq.Close(hObj, 0, function (err) {
                        if (err) {
                            console.error("Close ".concat(objectNameHint, " (handle ").concat(hObj, ") failed: MQCC=").concat(err.mqcc, ", MQRC=").concat(err.mqrc, ", Message=").concat(err.message));
                            reject(new Error("Close ".concat(objectNameHint, " failed: MQCC=").concat(err.mqcc, ", MQRC=").concat(err.mqrc, ", Message=").concat(err.message)));
                        }
                        else {
                            console.log("".concat(objectNameHint, " (handle ").concat(hObj, ") closed successfully."));
                            resolve();
                        }
                    });
                })];
        });
    });
}
// Put a message to a queue
function putMessage(hObj_1, message_1) {
    return __awaiter(this, arguments, void 0, function (hObj, message, queueNameHint) {
        if (queueNameHint === void 0) { queueNameHint = "queue"; }
        return __generator(this, function (_a) {
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    var mqmd = new mq.MQMD(); // Defaults are usually fine for basic put
                    var pmo = new mq.MQPMO();
                    pmo.Options = ibmmq_1.MQC.MQPMO_NO_SYNCPOINT | ibmmq_1.MQC.MQPMO_NEW_MSG_ID | ibmmq_1.MQC.MQPMO_NEW_CORREL_ID;
                    var msgBuffer = Buffer.from(message);
                    mq.Put(hObj, mqmd, pmo, msgBuffer, function (err) {
                        if (err) {
                            reject(new Error("Put message to ".concat(queueNameHint, " failed: MQCC=").concat(err.mqcc, ", MQRC=").concat(err.mqrc, ", Message=").concat(err.message)));
                        }
                        else {
                            console.log("Message put successfully to ".concat(queueNameHint, ": ").concat(message.substring(0, 50), "..."));
                            resolve();
                        }
                    });
                })];
        });
    });
}
// Generic Get function (used by getMessage and browseMessage)
function internalGetMessage(hObj_1, gmoOptions_1) {
    return __awaiter(this, arguments, void 0, function (hObj, gmoOptions, queueNameHint, operationHint) {
        var mqmd, gmo, MAX_MSG_LEN, buffer, length_1, message, mqErr, message;
        if (queueNameHint === void 0) { queueNameHint = "queue"; }
        if (operationHint === void 0) { operationHint = "Get"; }
        return __generator(this, function (_a) {
            mqmd = new mq.MQMD();
            gmo = new mq.MQGMO();
            gmo.Options = gmoOptions;
            gmo.WaitInterval = 0; // Default to NO_WAIT, can be overridden in gmoOptions
            MAX_MSG_LEN = 4 * 1024 * 1024;
            buffer = Buffer.alloc(MAX_MSG_LEN);
            try {
                length_1 = mq.GetSync(hObj, mqmd, gmo, buffer);
                if (length_1 !== undefined && length_1 > 0) {
                    message = buffer.toString('utf8', 0, length_1);
                    console.log("".concat(operationHint, " message from ").concat(queueNameHint, " (").concat(length_1, " bytes): ").concat(message.substring(0, 50), "..."));
                    return [2 /*return*/, message];
                }
                else {
                    console.log("No messages available on ".concat(queueNameHint, " for ").concat(operationHint, "."));
                    return [2 /*return*/, null];
                }
            }
            catch (err) {
                if (err instanceof Error && 'mqrc' in err) {
                    mqErr = err;
                    if (mqErr.mqrc === 2033) { // MQRC_NO_MSG_AVAILABLE
                        console.log("No messages available on ".concat(queueNameHint, " for ").concat(operationHint, "."));
                        return [2 /*return*/, null];
                    }
                    else if (mqErr.mqrc === 2079) { // MQRC_TRUNCATED_MSG_ACCEPTED
                        message = buffer.toString('utf8');
                        console.warn("".concat(operationHint, " message from ").concat(queueNameHint, " (TRUNCATED): ").concat(message.substring(0, 50), "..."));
                        return [2 /*return*/, message];
                    }
                    else {
                        throw new Error("".concat(operationHint, " message from ").concat(queueNameHint, " failed: MQCC=").concat(mqErr.mqcc, ", MQRC=").concat(mqErr.mqrc, ", Message=").concat(mqErr.message));
                    }
                }
                else {
                    throw err;
                }
            }
            return [2 /*return*/];
        });
    });
}
// Get a message from a queue (destructive)
function getMessage(hObj_1) {
    return __awaiter(this, arguments, void 0, function (hObj, queueNameHint) {
        var gmoOptions;
        if (queueNameHint === void 0) { queueNameHint = "queue"; }
        return __generator(this, function (_a) {
            gmoOptions = ibmmq_1.MQC.MQGMO_NO_SYNCPOINT | ibmmq_1.MQC.MQGMO_FAIL_IF_QUIESCING;
            return [2 /*return*/, internalGetMessage(hObj, gmoOptions, queueNameHint, "Get (destructive)")];
        });
    });
}
// Browse a message from a queue (non-destructive)
function browseMessage(hObj_1, browseGmoOption_1) {
    return __awaiter(this, arguments, void 0, function (hObj, browseGmoOption, queueNameHint) {
        var gmoOptions;
        if (queueNameHint === void 0) { queueNameHint = "queue"; }
        return __generator(this, function (_a) {
            gmoOptions = browseGmoOption | ibmmq_1.MQC.MQGMO_FAIL_IF_QUIESCING;
            return [2 /*return*/, internalGetMessage(hObj, gmoOptions, queueNameHint, "Browse")];
        });
    });
}
// Clear all messages from a queue using MQGET
function clearQueue(hConn, queueName) {
    return __awaiter(this, void 0, void 0, function () {
        var openedQ, messagesCleared, hObj, mqmd, gmo, buffer, length_2, mqErr, err_1, closeErr_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    console.log("Attempting to clear queue: ".concat(queueName));
                    openedQ = null;
                    messagesCleared = 0;
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 9]);
                    return [4 /*yield*/, openQueue(hConn, queueName, ibmmq_1.MQC.MQOO_INPUT_EXCLUSIVE | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 2:
                    openedQ = _a.sent();
                    hObj = openedQ.hObj;
                    mqmd = new mq.MQMD();
                    gmo = new mq.MQGMO();
                    gmo.Options = ibmmq_1.MQC.MQGMO_NO_WAIT | ibmmq_1.MQC.MQGMO_NO_SYNCPOINT | ibmmq_1.MQC.MQGMO_FAIL_IF_QUIESCING;
                    buffer = Buffer.alloc(1024);
                    while (true) {
                        try {
                            length_2 = mq.GetSync(hObj, mqmd, gmo, buffer);
                            if (length_2 !== undefined && length_2 >= 0) {
                                messagesCleared++;
                            }
                        }
                        catch (err) {
                            if (err instanceof Error && 'mqrc' in err) {
                                mqErr = err;
                                if (mqErr.mqrc === 2033) { // MQRC_NO_MSG_AVAILABLE
                                    break; // No more messages, exit loop
                                }
                                throw err; // Re-throw any other MQ errors
                            }
                            throw err; // Re-throw non-MQ errors
                        }
                    }
                    console.log("Queue ".concat(queueName, " cleared. ").concat(messagesCleared, " messages removed."));
                    return [3 /*break*/, 9];
                case 3:
                    err_1 = _a.sent();
                    console.error("Error during clearQueue for ".concat(queueName, ":"), err_1);
                    throw err_1; // Re-throw to be caught by main or caller
                case 4:
                    if (!(openedQ && openedQ.hObj)) return [3 /*break*/, 8];
                    _a.label = 5;
                case 5:
                    _a.trys.push([5, 7, , 8]);
                    return [4 /*yield*/, closeObject(openedQ.hObj, queueName)];
                case 6:
                    _a.sent();
                    return [3 /*break*/, 8];
                case 7:
                    closeErr_1 = _a.sent();
                    console.error("Error closing queue ".concat(queueName, " after clear attempt:"), closeErr_1);
                    return [3 /*break*/, 8];
                case 8: return [7 /*endfinally*/];
                case 9: return [2 /*return*/];
            }
        });
    });
}
// Get the current depth of a queue with authorization check
function getQueueDepth(hConn, queueName) {
    return __awaiter(this, void 0, void 0, function () {
        var openedQ, err_2, mqErr, closeErr_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    openedQ = null;
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, 4, 9]);
                    return [4 /*yield*/, openQueue(hConn, queueName, ibmmq_1.MQC.MQOO_INQUIRE | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 2:
                    // Open queue for inquire
                    openedQ = _a.sent();
                    return [2 /*return*/, new Promise(function (resolve, reject) {
                            var selectors = [new mq.MQAttr(ibmmq_1.MQC.MQIA_CURRENT_Q_DEPTH)];
                            mq.Inq(openedQ.hObj, selectors, function (err, jsSelectors) {
                                if (err) {
                                    if (err.mqrc === 2035) { // MQRC_NOT_AUTHORIZED
                                        resolve(null); // Skip unauthorized queues silently
                                    }
                                    else {
                                        reject(new Error("Failed to get depth for queue ".concat(queueName, ": MQCC=").concat(err.mqcc, ", MQRC=").concat(err.mqrc, ", Message=").concat(err.message)));
                                    }
                                }
                                else {
                                    resolve(jsSelectors[0].value);
                                }
                            });
                        })];
                case 3:
                    err_2 = _a.sent();
                    if (err_2 instanceof Error && 'mqrc' in err_2) {
                        mqErr = err_2;
                        if (mqErr.mqrc === 2035) { // MQRC_NOT_AUTHORIZED
                            return [2 /*return*/, null]; // Skip unauthorized queues silently
                        }
                    }
                    throw err_2;
                case 4:
                    if (!(openedQ && openedQ.hObj)) return [3 /*break*/, 8];
                    _a.label = 5;
                case 5:
                    _a.trys.push([5, 7, , 8]);
                    return [4 /*yield*/, closeObject(openedQ.hObj, queueName)];
                case 6:
                    _a.sent();
                    return [3 /*break*/, 8];
                case 7:
                    closeErr_2 = _a.sent();
                    console.error("Error closing queue ".concat(queueName, " after depth inquiry:"), closeErr_2);
                    return [3 /*break*/, 8];
                case 8: return [7 /*endfinally*/];
                case 9: return [2 /*return*/];
            }
        });
    });
}
// Get a list of all authorized queues
function listQueues(hConn) {
    return __awaiter(this, void 0, void 0, function () {
        var queues, seenQueues, queuePatterns, _i, queuePatterns_1, pattern, q, depth, currentName, nextName, nextQ, depth_1, err_3, mqErr, err_4, mqErr, commonQueues, _a, commonQueues_1, qName, q, depth, err_5;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    queues = [];
                    seenQueues = new Set();
                    queuePatterns = [
                        'DEV.*', // Development queues
                        'APP.*', // Application queues
                        'TEST.*', // Test queues
                        '*.*', // Generic queues
                        'SYSTEM.DEFAULT.*' // Default system queues
                    ];
                    _i = 0, queuePatterns_1 = queuePatterns;
                    _b.label = 1;
                case 1:
                    if (!(_i < queuePatterns_1.length)) return [3 /*break*/, 21];
                    pattern = queuePatterns_1[_i];
                    _b.label = 2;
                case 2:
                    _b.trys.push([2, 19, , 20]);
                    return [4 /*yield*/, openQueue(hConn, pattern, ibmmq_1.MQC.MQOO_INQUIRE | ibmmq_1.MQC.MQOO_BROWSE | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 3:
                    q = _b.sent();
                    if (!q) return [3 /*break*/, 18];
                    _b.label = 4;
                case 4:
                    _b.trys.push([4, , 16, 18]);
                    if (!!seenQueues.has(q.name)) return [3 /*break*/, 15];
                    seenQueues.add(q.name);
                    return [4 /*yield*/, getQueueDepth(hConn, q.name)];
                case 5:
                    depth = _b.sent();
                    if (depth !== null) {
                        queues.push({
                            name: q.name,
                            depth: depth,
                            type: 'Local'
                        });
                    }
                    currentName = q.name;
                    _b.label = 6;
                case 6:
                    if (!true) return [3 /*break*/, 15];
                    _b.label = 7;
                case 7:
                    _b.trys.push([7, 13, , 14]);
                    nextName = incrementQueueName(currentName);
                    if (!nextName)
                        return [3 /*break*/, 15];
                    return [4 /*yield*/, openQueue(hConn, nextName, ibmmq_1.MQC.MQOO_INQUIRE | ibmmq_1.MQC.MQOO_BROWSE | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 8:
                    nextQ = _b.sent();
                    if (!(nextQ && !seenQueues.has(nextQ.name))) return [3 /*break*/, 11];
                    seenQueues.add(nextQ.name);
                    return [4 /*yield*/, getQueueDepth(hConn, nextQ.name)];
                case 9:
                    depth_1 = _b.sent();
                    if (depth_1 !== null) {
                        queues.push({
                            name: nextQ.name,
                            depth: depth_1,
                            type: 'Local'
                        });
                    }
                    return [4 /*yield*/, closeObject(nextQ.hObj)];
                case 10:
                    _b.sent();
                    currentName = nextQ.name;
                    return [3 /*break*/, 12];
                case 11: return [3 /*break*/, 15];
                case 12: return [3 /*break*/, 14];
                case 13:
                    err_3 = _b.sent();
                    if (err_3 instanceof Error && 'mqrc' in err_3) {
                        mqErr = err_3;
                        if (mqErr.mqrc === 2085 || // MQRC_UNKNOWN_OBJECT_NAME
                            mqErr.mqrc === 2035) { // MQRC_NOT_AUTHORIZED
                            // If queue doesn't exist or we're not authorized, try the next pattern
                            return [3 /*break*/, 15];
                        }
                    }
                    // For other errors, just try the next queue
                    return [3 /*break*/, 15];
                case 14: return [3 /*break*/, 6];
                case 15: return [3 /*break*/, 18];
                case 16: return [4 /*yield*/, closeObject(q.hObj)];
                case 17:
                    _b.sent();
                    return [7 /*endfinally*/];
                case 18: return [3 /*break*/, 20];
                case 19:
                    err_4 = _b.sent();
                    if (err_4 instanceof Error && 'mqrc' in err_4) {
                        mqErr = err_4;
                        if (mqErr.mqrc !== 2085 && // MQRC_UNKNOWN_OBJECT_NAME
                            mqErr.mqrc !== 2035) { // MQRC_NOT_AUTHORIZED
                            // Log non-authorization and non-unknown object errors
                            console.warn("Error accessing queue pattern ".concat(pattern, ":"), mqErr.message);
                        }
                    }
                    // Continue with next pattern regardless of error
                    return [3 /*break*/, 20];
                case 20:
                    _i++;
                    return [3 /*break*/, 1];
                case 21:
                    commonQueues = [
                        'DEV.QUEUE.1',
                        'DEV.QUEUE.2',
                        'DEV.QUEUE.3',
                        'APP.QUEUE.1',
                        'TEST.QUEUE'
                    ];
                    _a = 0, commonQueues_1 = commonQueues;
                    _b.label = 22;
                case 22:
                    if (!(_a < commonQueues_1.length)) return [3 /*break*/, 30];
                    qName = commonQueues_1[_a];
                    if (!!seenQueues.has(qName)) return [3 /*break*/, 29];
                    _b.label = 23;
                case 23:
                    _b.trys.push([23, 28, , 29]);
                    return [4 /*yield*/, openQueue(hConn, qName, ibmmq_1.MQC.MQOO_INQUIRE | ibmmq_1.MQC.MQOO_BROWSE | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 24:
                    q = _b.sent();
                    if (!q) return [3 /*break*/, 27];
                    seenQueues.add(qName);
                    return [4 /*yield*/, getQueueDepth(hConn, qName)];
                case 25:
                    depth = _b.sent();
                    if (depth !== null) {
                        queues.push({
                            name: qName,
                            depth: depth,
                            type: 'Local'
                        });
                    }
                    return [4 /*yield*/, closeObject(q.hObj)];
                case 26:
                    _b.sent();
                    _b.label = 27;
                case 27: return [3 /*break*/, 29];
                case 28:
                    err_5 = _b.sent();
                    // Ignore errors for individual queues
                    return [3 /*break*/, 29];
                case 29:
                    _a++;
                    return [3 /*break*/, 22];
                case 30: return [2 /*return*/, queues];
            }
        });
    });
}
// Helper function to increment queue name for iteration
function incrementQueueName(qName) {
    // Find the last number in the queue name
    var match = qName.match(/(\d+)$/);
    if (match) {
        var num = parseInt(match[1], 10);
        var prefix = qName.slice(0, -match[1].length);
        return prefix + (num + 1).toString().padStart(match[1].length, '0');
    }
    // If no number found, try appending .1
    if (!qName.includes('.')) {
        return qName + '.1';
    }
    return null;
}
// Disconnect from the Queue Manager
function disconnect(hConn_1) {
    return __awaiter(this, arguments, void 0, function (hConn, qmNameHint) {
        if (qmNameHint === void 0) { qmNameHint = "QueueManager"; }
        return __generator(this, function (_a) {
            return [2 /*return*/, new Promise(function (resolve, reject) {
                    mq.Disc(hConn, function (err) {
                        if (err) {
                            reject(new Error("Disconnect from ".concat(qmNameHint, " failed: MQCC=").concat(err.mqcc, ", MQRC=").concat(err.mqrc, ", Message=").concat(err.message)));
                        }
                        else {
                            console.log("Disconnected from ".concat(qmNameHint, "."));
                            resolve();
                        }
                    });
                })];
        });
    });
}
// Main function to demonstrate operations
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var mqConn, openedQ, queues, testQueueName, messageToPut, browsedMsg, gotMsg, err_6, mqErr, closeErr_3, discErr_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    mqConn = null;
                    openedQ = null;
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 26, 27, 36]);
                    console.log("--- Connecting to Queue Manager ---");
                    return [4 /*yield*/, connectToQueueManager()];
                case 2:
                    mqConn = _a.sent(); // --- List Authorized Queues and Their Depths ---
                    console.log("\n--- Listing Authorized Queues and Their Depths ---");
                    return [4 /*yield*/, listQueues(mqConn.hConn)];
                case 3:
                    queues = _a.sent();
                    if (queues.length > 0) {
                        console.log("Found ".concat(queues.length, " authorized queues:"));
                        queues.forEach(function (q) {
                            console.log("Queue: ".concat(q.name, ", Type: ").concat(q.type, ", Current Depth: ").concat(q.depth));
                        });
                    }
                    else {
                        console.log("No authorized queues found.");
                    }
                    testQueueName = config.queueName;
                    // --- Test PUT ---
                    console.log("\n--- Testing PUT to ".concat(testQueueName, " ---"));
                    return [4 /*yield*/, openQueue(mqConn.hConn, testQueueName, ibmmq_1.MQC.MQOO_OUTPUT | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 4:
                    openedQ = _a.sent();
                    messageToPut = "Hello from TypeScript at ".concat(new Date().toISOString());
                    return [4 /*yield*/, putMessage(openedQ.hObj, messageToPut, testQueueName)];
                case 5:
                    _a.sent();
                    return [4 /*yield*/, putMessage(openedQ.hObj, "Second message for browsing and getting.", testQueueName)];
                case 6:
                    _a.sent();
                    return [4 /*yield*/, closeObject(openedQ.hObj, testQueueName)];
                case 7:
                    _a.sent();
                    openedQ = null;
                    // --- Test BROWSE ---
                    console.log("\n--- Testing BROWSE on ".concat(testQueueName, " ---"));
                    return [4 /*yield*/, openQueue(mqConn.hConn, testQueueName, ibmmq_1.MQC.MQOO_INPUT_SHARED | ibmmq_1.MQC.MQOO_BROWSE | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 8:
                    openedQ = _a.sent();
                    return [4 /*yield*/, browseMessage(openedQ.hObj, ibmmq_1.MQC.MQGMO_BROWSE_FIRST, testQueueName)];
                case 9:
                    browsedMsg = _a.sent();
                    if (browsedMsg)
                        console.log("First browsed message content:", browsedMsg.substring(0, 100));
                    return [4 /*yield*/, browseMessage(openedQ.hObj, ibmmq_1.MQC.MQGMO_BROWSE_NEXT, testQueueName)];
                case 10:
                    browsedMsg = _a.sent();
                    if (browsedMsg)
                        console.log("Second browsed message content:", browsedMsg.substring(0, 100));
                    return [4 /*yield*/, closeObject(openedQ.hObj, testQueueName)];
                case 11:
                    _a.sent();
                    openedQ = null;
                    // --- Test GET (destructive) ---
                    console.log("\n--- Testing GET from ".concat(testQueueName, " ---"));
                    return [4 /*yield*/, openQueue(mqConn.hConn, testQueueName, ibmmq_1.MQC.MQOO_INPUT_EXCLUSIVE | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 12:
                    openedQ = _a.sent(); // Exclusive for GET
                    return [4 /*yield*/, getMessage(openedQ.hObj, testQueueName)];
                case 13:
                    gotMsg = _a.sent();
                    if (gotMsg)
                        console.log("First got message content:", gotMsg.substring(0, 100));
                    return [4 /*yield*/, getMessage(openedQ.hObj, testQueueName)];
                case 14:
                    gotMsg = _a.sent(); // Attempt to get the second message
                    if (gotMsg)
                        console.log("Second got message content:", gotMsg.substring(0, 100));
                    return [4 /*yield*/, getMessage(openedQ.hObj, testQueueName)];
                case 15:
                    gotMsg = _a.sent(); // Attempt to get a third (should be none)
                    if (!gotMsg)
                        console.log("No more messages to get, as expected.");
                    return [4 /*yield*/, closeObject(openedQ.hObj, testQueueName)];
                case 16:
                    _a.sent();
                    openedQ = null;
                    // --- Test CLEAR QUEUE ---
                    console.log("\n--- Testing CLEAR QUEUE for ".concat(testQueueName, " ---"));
                    return [4 /*yield*/, openQueue(mqConn.hConn, testQueueName, ibmmq_1.MQC.MQOO_OUTPUT | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 17:
                    // Put some messages to clear
                    openedQ = _a.sent();
                    return [4 /*yield*/, putMessage(openedQ.hObj, "Message 1 for clearing", testQueueName)];
                case 18:
                    _a.sent();
                    return [4 /*yield*/, putMessage(openedQ.hObj, "Message 2 for clearing", testQueueName)];
                case 19:
                    _a.sent();
                    return [4 /*yield*/, putMessage(openedQ.hObj, "Message 3 for clearing", testQueueName)];
                case 20:
                    _a.sent();
                    return [4 /*yield*/, closeObject(openedQ.hObj, testQueueName)];
                case 21:
                    _a.sent();
                    openedQ = null;
                    return [4 /*yield*/, clearQueue(mqConn.hConn, testQueueName)];
                case 22:
                    _a.sent();
                    return [4 /*yield*/, openQueue(mqConn.hConn, testQueueName, ibmmq_1.MQC.MQOO_INPUT_SHARED | ibmmq_1.MQC.MQOO_BROWSE | ibmmq_1.MQC.MQOO_FAIL_IF_QUIESCING)];
                case 23:
                    // Verify queue is empty
                    openedQ = _a.sent();
                    return [4 /*yield*/, browseMessage(openedQ.hObj, ibmmq_1.MQC.MQGMO_BROWSE_FIRST, testQueueName)];
                case 24:
                    browsedMsg = _a.sent();
                    if (!browsedMsg) {
                        console.log("Queue ".concat(testQueueName, " is empty after clearing, as expected."));
                    }
                    else {
                        console.warn("Queue ".concat(testQueueName, " still has messages after clearing!"));
                    }
                    return [4 /*yield*/, closeObject(openedQ.hObj, testQueueName)];
                case 25:
                    _a.sent();
                    openedQ = null;
                    return [3 /*break*/, 36];
                case 26:
                    err_6 = _a.sent();
                    if (err_6 instanceof Error && 'mqcc' in err_6 && 'mqrc' in err_6) {
                        mqErr = err_6;
                        console.error("\n--- MQ Operation Failed ---");
                        console.error("Message: ".concat(mqErr.message));
                        console.error("MQCC: ".concat(mqErr.mqcc));
                        console.error("MQRC: ".concat(mqErr.mqrc));
                        if (mqErr.stack)
                            console.error("Stack: ".concat(mqErr.stack));
                    }
                    else if (err_6 instanceof Error) {
                        console.error("\n--- JavaScript Error ---");
                        console.error("Message: ".concat(err_6.message));
                        if (err_6.stack)
                            console.error("Stack: ".concat(err_6.stack));
                    }
                    else {
                        console.error('\n--- Unknown Error Type ---');
                        console.error(err_6);
                    }
                    return [3 /*break*/, 36];
                case 27:
                    console.log("\n--- Cleaning up ---");
                    if (!(openedQ && openedQ.hObj)) return [3 /*break*/, 31];
                    _a.label = 28;
                case 28:
                    _a.trys.push([28, 30, , 31]);
                    return [4 /*yield*/, closeObject(openedQ.hObj, openedQ.name)];
                case 29:
                    _a.sent();
                    return [3 /*break*/, 31];
                case 30:
                    closeErr_3 = _a.sent();
                    return [3 /*break*/, 31];
                case 31:
                    if (!mqConn) return [3 /*break*/, 35];
                    _a.label = 32;
                case 32:
                    _a.trys.push([32, 34, , 35]);
                    return [4 /*yield*/, disconnect(mqConn.hConn, mqConn.queueManagerName)];
                case 33:
                    _a.sent();
                    return [3 /*break*/, 35];
                case 34:
                    discErr_1 = _a.sent();
                    return [3 /*break*/, 35];
                case 35:
                    console.log("--- Main function finished ---");
                    return [7 /*endfinally*/];
                case 36: return [2 /*return*/];
            }
        });
    });
}
// Run the main function
main();
