# Change Log

All notable changes to the "mqexplorer" extension will be documented in this file.

## [0.0.1] - 2023-10-15

### Added
- Initial release with basic IBM MQ functionality
- Connection management for IBM MQ
  - Create, edit, and delete connection profiles
  - Securely store credentials
  - Connect to and disconnect from queue managers
- Queue Explorer
  - Browse queues in a connected queue manager
  - View queue properties
  - Clear queues
- Message Browsing
  - Browse messages in a queue (non-destructive peek)
  - View message payload in text or hex format
  - View message properties and headers
  - Save message payload to a file
- Message Publishing
  - Put messages to queues
  - Set message properties (correlation ID, reply-to queue, etc.)
  - Load message payload from a file

  ## [0.0.2] - 2023-10-16
  ### Added
  - Support for multiple messaging providers (RabbitMQ, Kafka, ActiveMQ, Azure Service Bus, AWS SQS)
  - Provider-specific connection profiles and forms
  - Provider-specific message browsing and putting
  - Provider-specific queue and topic management
  - Provider-specific channel management
  - Provider-specific message properties and headers
  - Provider-specific message formats (JSON, XML, etc.)
  - Provider-specific security features (TLS, authentication, etc.)

  ##  [0.0.3] - 2023-10-17
  ### Added
  - Support for message filtering and searching
  - Support for batch operations (delete multiple messages, etc.)
  - Support for message templates
  - Support for message tracing and debugging
  - Support for performance optimization (pagination, etc.)
  - Support for integration with development workflow (e.g., testing, debugging)
  - Support for customization and settings
  - Support for documentation and troubleshooting
  - Support for testing and refinement

  ## [0.0.4] - 2023-10-18
  ### Added
  - Support for message conversion and transformation
  - Support for message encryption and decryption
  - Support for message compression and decompression
  - Support for message deduplication
  - Support for message prioritization
  - Support for message expiration
  - Support for message persistence
  - Support for message delivery guarantees
  - Support for message ordering
  - Support for message transactions
  - Support for message auditing and logging

  ## [0.0.5] - 2023-10-19
  ### Added
  - Support for message encryption and decryption
  - Support for message compression and decompression
  - Support for message deduplication
  - Support for message prioritization
  - Support for message expiration
  - Support for message persistence
  - Support for message delivery guarantees
  - Support for message ordering
  - Support for message transactions
  - Support for message auditing and logging
  - Support for message tracing and debugging
  - Support for performance optimization (pagination, etc.)
  - Support for integration with development workflow (e.g., testing, debugging)
  - Support for customization and settings
  - Support for documentation and troubleshooting
  - Support for testing and refinement

  ## [0.0.6] - 2023-10-20
  ### Added
  - Support for message encryption and decryption
  - Support for message compression and decompression
  - Support for message deduplication
  - Support for message prioritization
  - Support for message expiration
  - Support for message persistence
  - Support for message delivery guarantees
  - Support for message ordering
  - Support for message transactions
  - Support for message auditing and logging
  - Support for message tracing and debugging
  - Support for performance optimization (pagination, etc.)
  - Support for integration with development workflow (e.g., testing, debugging)
  - Support for customization and settings
  - Support for documentation and troubleshooting
  - Support for testing and refinement

  ## [0.0.7] - 2023-10-21
  ### Added 
  - Support for message encryption and decryption
  - Support for message compression and decompression
  - Support for message deduplication
  - Support for message prioritization
  - Support for message expiration
  - Support for message persistence
  - Support for message delivery guarantees
  - Support for message ordering
  - Support for message transactions
  - Support for message auditing and logging
  - Support for message tracing and debugging