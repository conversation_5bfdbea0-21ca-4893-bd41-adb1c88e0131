//@ts-check

'use strict';

const path = require('path');

/**@type {import('webpack').Configuration}*/
const config = {
  target: 'node', // vscode extensions run in a Node.js-context
  mode: process.env.NODE_ENV === 'production' ? 'production' : 'development', // Use production mode for packaging

  entry: './src/extension.ts', // the entry point of this extension
  output: {
    // the bundle is stored in the 'dist' folder (check package.json),
    path: path.resolve(__dirname, 'dist'),
    filename: 'extension.js',
    libraryTarget: 'commonjs2',
    devtoolModuleFilenameTemplate: '../[resource-path]'
  },
  devtool: 'source-map',
  externals: {
    vscode: 'commonjs vscode', // the vscode-module is created on-the-fly and must be excluded
    'node-addon-api': 'commonjs node-addon-api',
    'node-gyp': 'commonjs node-gyp',
    'node-gyp-build': 'commonjs node-gyp-build',
    'prebuildify': 'commonjs prebuildify',
    'proxy-agent': 'commonjs proxy-agent',
    'env-paths': 'commonjs env-paths',
    'exponential-backoff': 'commonjs exponential-backoff',
    'graceful-fs': 'commonjs graceful-fs',
    'make-fetch-happen': 'commonjs make-fetch-happen',
    'nopt': 'commonjs nopt',
    'proc-log': 'commonjs proc-log',
    'semver': 'commonjs semver',
    'tar': 'commonjs tar',
    'tinyglobby': 'commonjs tinyglobby',
    'which': 'commonjs which',
    '@npmcli/agent': 'commonjs @npmcli/agent',
    'cacache': 'commonjs cacache',
    'http-cache-semantics': 'commonjs http-cache-semantics',
    'minipass': 'commonjs minipass',
    'minipass-fetch': 'commonjs minipass-fetch',
    'minipass-flush': 'commonjs minipass-flush',
    'minipass-pipeline': 'commonjs minipass-pipeline',
    'negotiator': 'commonjs negotiator',
    'promise-retry': 'commonjs promise-retry',
    'ssri': 'commonjs ssri',
    'agent-base': 'commonjs agent-base',
    'http-proxy-agent': 'commonjs http-proxy-agent',
    'https-proxy-agent': 'commonjs https-proxy-agent',
    'lru-cache': 'commonjs lru-cache',
    'socks-proxy-agent': 'commonjs socks-proxy-agent',
    'debug': 'commonjs debug',
    'ms': 'commonjs ms',
    'socks': 'commonjs socks',
    'ip-address': 'commonjs ip-address',
    'smart-buffer': 'commonjs smart-buffer',
    'jsbn': 'commonjs jsbn',
    'sprintf-js': 'commonjs sprintf-js',
    '@npmcli/fs': 'commonjs @npmcli/fs',
    'fs-minipass': 'commonjs fs-minipass',
    'glob': 'commonjs glob',
    'minipass-collect': 'commonjs minipass-collect',
    'p-map': 'commonjs p-map',
    'unique-filename': 'commonjs unique-filename',
    'foreground-child': 'commonjs foreground-child',
    'jackspeak': 'commonjs jackspeak',
    'minimatch': 'commonjs minimatch',
    'package-json-from-dist': 'commonjs package-json-from-dist',
    'path-scurry': 'commonjs path-scurry',
    'cross-spawn': 'commonjs cross-spawn',
    'signal-exit': 'commonjs signal-exit',
    'path-key': 'commonjs path-key',
    'shebang-command': 'commonjs shebang-command',
    'shebang-regex': 'commonjs shebang-regex',
    'isexe': 'commonjs isexe',
    '@isaacs/cliui': 'commonjs @isaacs/cliui',
    'brace-expansion': 'commonjs brace-expansion',
    'balanced-match': 'commonjs balanced-match',
    '@isaacs/fs-minipass': 'commonjs @isaacs/fs-minipass',
    'chownr': 'commonjs chownr',
    'minizlib': 'commonjs minizlib',
    'mkdirp': 'commonjs mkdirp',
    'yallist': 'commonjs yallist',
    'unique-slug': 'commonjs unique-slug',
    'imurmurhash': 'commonjs imurmurhash',
    'minipass-sized': 'commonjs minipass-sized',
    'err-code': 'commonjs err-code',
    'retry': 'commonjs retry',
    'abbrev': 'commonjs abbrev',
    'minimist': 'commonjs minimist',
    'mkdirp-classic': 'commonjs mkdirp-classic',
    'node-abi': 'commonjs node-abi',
    'npm-run-path': 'commonjs npm-run-path',
    'pump': 'commonjs pump',
    'tar-fs': 'commonjs tar-fs',
    'end-of-stream': 'commonjs end-of-stream',
    'once': 'commonjs once',
    'wrappy': 'commonjs wrappy',
    'tar-stream': 'commonjs tar-stream',
    'bl': 'commonjs bl',
    'fs-constants': 'commonjs fs-constants',
    'inherits': 'commonjs inherits',
    'readable-stream': 'commonjs readable-stream',
    'buffer': 'commonjs buffer',
    'base64-js': 'commonjs base64-js',
    'ieee754': 'commonjs ieee754',
    'core-util-is': 'commonjs core-util-is',
    'isarray': 'commonjs isarray',
    'process-nextick-args': 'commonjs process-nextick-args',
    'safe-buffer': 'commonjs safe-buffer',
    'string_decoder': 'commonjs string_decoder',
    'util-deprecate': 'commonjs util-deprecate',
    'pac-proxy-agent': 'commonjs pac-proxy-agent',
    'proxy-from-env': 'commonjs proxy-from-env',
    '@tootallnate/quickjs-emscripten': 'commonjs @tootallnate/quickjs-emscripten',
    'get-uri': 'commonjs get-uri',
    'pac-resolver': 'commonjs pac-resolver',
    'basic-ftp': 'commonjs basic-ftp',
    'data-uri-to-buffer': 'commonjs data-uri-to-buffer',
    'degenerator': 'commonjs degenerator',
    'netmask': 'commonjs netmask',
    'ast-types': 'commonjs ast-types',
    'escodegen': 'commonjs escodegen',
    'esprima': 'commonjs esprima',
    'tslib': 'commonjs tslib',
    'estraverse': 'commonjs estraverse',
    'esutils': 'commonjs esutils',
    'ibmmq': 'commonjs ibmmq',
    'buffer-more-ints': 'commonjs buffer-more-ints',
    'url-parse': 'commonjs url-parse',
    'querystringify': 'commonjs querystringify',
    'requires-port': 'commonjs requires-port',
    'amqplib': 'commonjs amqplib',
    'kafkajs': 'commonjs kafkajs',
    'qs': 'commonjs qs',
    'stompit': 'commonjs stompit',
    '@azure/abort-controller': 'commonjs @azure/abort-controller',
    '@azure/core-amqp': 'commonjs @azure/core-amqp',
    '@azure/core-auth': 'commonjs @azure/core-auth',
    '@azure/core-client': 'commonjs @azure/core-client',
    '@azure/core-rest-pipeline': 'commonjs @azure/core-rest-pipeline',
    '@azure/core-tracing': 'commonjs @azure/core-tracing',
    '@azure/core-paging': 'commonjs @azure/core-paging',
    '@azure/core-util': 'commonjs @azure/core-util',
    '@azure/core-xml': 'commonjs @azure/core-xml',
    '@azure/logger': 'commonjs @azure/logger',
    '@types/is-buffer': 'commonjs @types/is-buffer',
    'is-buffer': 'commonjs is-buffer',
    'jssha': 'commonjs jssha',
    'long': 'commonjs long',
    'process': 'commonjs process',
    'rhea-promise': 'commonjs rhea-promise',
    'events': 'commonjs events',
    'rhea': 'commonjs rhea',
    '@typespec/ts-http-runtime': 'commonjs @typespec/ts-http-runtime',
    'fast-xml-parser': 'commonjs fast-xml-parser',
    '@types/node': 'commonjs @types/node',
    'undici-types': 'commonjs undici-types',
    '@azure/service-bus': 'commonjs @azure/service-bus',
    '@azure/msal-browser': 'commonjs @azure/msal-browser',
    '@azure/msal-node': 'commonjs @azure/msal-node',
    'open': 'commonjs open',
    '@azure/msal-common': 'commonjs @azure/msal-common',
    'jsonwebtoken': 'commonjs jsonwebtoken',
    'uuid': 'commonjs uuid',
    'jws': 'commonjs jws',
    'lodash.includes': 'commonjs lodash.includes',
    'lodash.isboolean': 'commonjs lodash.isboolean',
    'lodash.isinteger': 'commonjs lodash.isinteger',
    'lodash.isnumber': 'commonjs lodash.isnumber',
    'lodash.isplainobject': 'commonjs lodash.isplainobject',
    'lodash.isstring': 'commonjs lodash.isstring',
    'lodash.once': 'commonjs lodash.once',
    'jwa': 'commonjs jwa',
    'buffer-equal-constant-time': 'commonjs buffer-equal-constant-time',
    'ecdsa-sig-formatter': 'commonjs ecdsa-sig-formatter',
    '@azure/identity': 'commonjs @azure/identity',
    '@aws-crypto/sha256-browser': 'commonjs @aws-crypto/sha256-browser',
    '@aws-crypto/sha256-js': 'commonjs @aws-crypto/sha256-js',
    '@aws-sdk/core': 'commonjs @aws-sdk/core',
    '@aws-sdk/credential-provider-node': 'commonjs @aws-sdk/credential-provider-node',
    '@aws-sdk/middleware-host-header': 'commonjs @aws-sdk/middleware-host-header',
    '@aws-sdk/middleware-logger': 'commonjs @aws-sdk/middleware-logger',
    '@aws-sdk/middleware-recursion-detection': 'commonjs @aws-sdk/middleware-recursion-detection',
    '@aws-sdk/middleware-sdk-sqs': 'commonjs @aws-sdk/middleware-sdk-sqs',
    '@aws-sdk/middleware-user-agent': 'commonjs @aws-sdk/middleware-user-agent',
    '@aws-sdk/region-config-resolver': 'commonjs @aws-sdk/region-config-resolver',
    '@aws-sdk/types': 'commonjs @aws-sdk/types',
    '@aws-sdk/util-endpoints': 'commonjs @aws-sdk/util-endpoints',
    '@aws-sdk/util-user-agent-browser': 'commonjs @aws-sdk/util-user-agent-browser',
    '@aws-sdk/util-user-agent-node': 'commonjs @aws-sdk/util-user-agent-node',
    '@smithy/config-resolver': 'commonjs @smithy/config-resolver',
    '@smithy/core': 'commonjs @smithy/core',
    '@smithy/fetch-http-handler': 'commonjs @smithy/fetch-http-handler',
    '@smithy/hash-node': 'commonjs @smithy/hash-node',
    '@smithy/invalid-dependency': 'commonjs @smithy/invalid-dependency',
    '@smithy/md5-js': 'commonjs @smithy/md5-js',
    '@smithy/middleware-content-length': 'commonjs @smithy/middleware-content-length',
    '@smithy/middleware-endpoint': 'commonjs @smithy/middleware-endpoint',
    '@smithy/middleware-retry': 'commonjs @smithy/middleware-retry',
    '@smithy/middleware-serde': 'commonjs @smithy/middleware-serde',
    '@smithy/middleware-stack': 'commonjs @smithy/middleware-stack',
    '@smithy/node-config-provider': 'commonjs @smithy/node-config-provider',
    '@smithy/node-http-handler': 'commonjs @smithy/node-http-handler',
    '@smithy/protocol-http': 'commonjs @smithy/protocol-http',
    '@smithy/smithy-client': 'commonjs @smithy/smithy-client',
    '@smithy/types': 'commonjs @smithy/types',
    '@smithy/url-parser': 'commonjs @smithy/url-parser',
    '@smithy/util-base64': 'commonjs @smithy/util-base64',
    '@smithy/util-body-length-browser': 'commonjs @smithy/util-body-length-browser',
    '@smithy/util-body-length-node': 'commonjs @smithy/util-body-length-node',
    '@smithy/util-defaults-mode-browser': 'commonjs @smithy/util-defaults-mode-browser',
    '@smithy/util-defaults-mode-node': 'commonjs @smithy/util-defaults-mode-node',
    '@smithy/util-endpoints': 'commonjs @smithy/util-endpoints',
    '@smithy/util-middleware': 'commonjs @smithy/util-middleware',
    '@smithy/util-retry': 'commonjs @smithy/util-retry',
    '@smithy/util-utf8': 'commonjs @smithy/util-utf8',
    '@aws-crypto/supports-web-crypto': 'commonjs @aws-crypto/supports-web-crypto',
    '@aws-crypto/util': 'commonjs @aws-crypto/util',
    '@aws-sdk/util-locate-window': 'commonjs @aws-sdk/util-locate-window',
    '@smithy/util-buffer-from': 'commonjs @smithy/util-buffer-from',
    '@smithy/is-array-buffer': 'commonjs @smithy/is-array-buffer',
    '@smithy/property-provider': 'commonjs @smithy/property-provider',
    '@smithy/signature-v4': 'commonjs @smithy/signature-v4',
    '@smithy/util-stream': 'commonjs @smithy/util-stream',
    '@smithy/util-hex-encoding': 'commonjs @smithy/util-hex-encoding',
    '@smithy/querystring-builder': 'commonjs @smithy/querystring-builder',
    '@smithy/util-uri-escape': 'commonjs @smithy/util-uri-escape',
    '@smithy/abort-controller': 'commonjs @smithy/abort-controller',
    '@smithy/shared-ini-file-loader': 'commonjs @smithy/shared-ini-file-loader',
    '@smithy/querystring-parser': 'commonjs @smithy/querystring-parser',
    '@aws-sdk/credential-provider-env': 'commonjs @aws-sdk/credential-provider-env',
    '@aws-sdk/credential-provider-http': 'commonjs @aws-sdk/credential-provider-http',
    '@aws-sdk/credential-provider-ini': 'commonjs @aws-sdk/credential-provider-ini',
    '@aws-sdk/credential-provider-process': 'commonjs @aws-sdk/credential-provider-process',
    '@aws-sdk/credential-provider-sso': 'commonjs @aws-sdk/credential-provider-sso',
    '@aws-sdk/credential-provider-web-identity': 'commonjs @aws-sdk/credential-provider-web-identity',
    '@smithy/credential-provider-imds': 'commonjs @smithy/credential-provider-imds',
    '@aws-sdk/nested-clients': 'commonjs @aws-sdk/nested-clients',
    '@aws-sdk/client-sso': 'commonjs @aws-sdk/client-sso',
    '@aws-sdk/token-providers': 'commonjs @aws-sdk/token-providers',
    '@smithy/util-config-provider': 'commonjs @smithy/util-config-provider',
    'bowser': 'commonjs bowser',
    '@smithy/service-error-classification': 'commonjs @smithy/service-error-classification',
    '@aws-sdk/client-sqs': 'commonjs @aws-sdk/client-sqs'
  },
  resolve: {
    // support reading TypeScript and JavaScript files
    extensions: ['.ts', '.js']
  },
  module: {
    rules: [
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        use: [
          {
            loader: 'ts-loader'
          }
        ]
      }
    ]
  }
};

module.exports = config;
