# Exclude everything by default
**/*

# Only include the necessary files
!dist/**
!package.json
!README.md
!LICENSE
!images/**

# Include only necessary files from node_modules
!node_modules/ibmmq/lib/mqidefs.js
!node_modules/ibmmq/lib/mqitypes.js
!node_modules/ibmmq/lib/mqistrings.js
!node_modules/ibmmq/lib/mqiattributes.js
!node_modules/ibmmq/lib/mqicodes.js
!node_modules/ibmmq/lib/mqi.js
!node_modules/ibmmq/lib/mqmho.js
!node_modules/ibmmq/lib/mqgmo.js
!node_modules/ibmmq/lib/mqpmo.js
!node_modules/ibmmq/lib/mqmd.js
!node_modules/ibmmq/lib/mqod.js
!node_modules/ibmmq/lib/mqsco.js
!node_modules/ibmmq/lib/mqcsp.js
!node_modules/ibmmq/lib/mqcno.js
!node_modules/ibmmq/lib/mqcbc.js
!node_modules/ibmmq/lib/mqctl.js
!node_modules/ibmmq/lib/mqsd.js
!node_modules/ibmmq/lib/mqsts.js
!node_modules/ibmmq/lib/mqbno.js
!node_modules/ibmmq/lib/mqcsp.js
!node_modules/ibmmq/prebuilds/win32-x64/node.napi.node
!node_modules/ibmmq/package.json

!node_modules/amqplib/lib/connection.js
!node_modules/amqplib/lib/channel.js
!node_modules/amqplib/lib/callback_model.js
!node_modules/amqplib/lib/channel_model.js
!node_modules/amqplib/lib/defs.js
!node_modules/amqplib/lib/format.js
!node_modules/amqplib/package.json

!node_modules/kafkajs/lib/index.js
!node_modules/kafkajs/lib/client.js
!node_modules/kafkajs/lib/producer.js
!node_modules/kafkajs/lib/consumer.js
!node_modules/kafkajs/lib/admin.js
!node_modules/kafkajs/package.json

!node_modules/stompit/lib/client.js
!node_modules/stompit/lib/connect.js
!node_modules/stompit/lib/frame.js
!node_modules/stompit/lib/transaction.js
!node_modules/stompit/package.json

!node_modules/@azure/service-bus/dist/index.js
!node_modules/@azure/service-bus/dist/index.js.map
!node_modules/@azure/service-bus/package.json

!node_modules/@azure/identity/dist/index.js
!node_modules/@azure/identity/dist/index.js.map
!node_modules/@azure/identity/package.json

!node_modules/@aws-sdk/client-sqs/dist/index.js
!node_modules/@aws-sdk/client-sqs/dist/index.js.map
!node_modules/@aws-sdk/client-sqs/package.json

!node_modules/uuid/dist/index.js
!node_modules/uuid/dist/index.js.map
!node_modules/uuid/package.json
