<?xml version="1.0" encoding="UTF-8"?>
<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- MQ Icon - Blue cylinder with yellow banner -->
  <defs>
    <linearGradient id="cylinderGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2E5BBA;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#4A7BC8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E5BBA;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bannerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#F4B942;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F4B942;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Cylinder body -->
  <rect x="3" y="4" width="18" height="16" rx="2" ry="2" fill="url(#cylinderGradient)" stroke="#1E3A8A" stroke-width="0.5"/>
  
  <!-- Cylinder top -->
  <ellipse cx="12" cy="4" rx="9" ry="2" fill="#4A7BC8" stroke="#1E3A8A" stroke-width="0.5"/>
  
  <!-- Yellow banner -->
  <rect x="4" y="10" width="16" height="6" rx="1" ry="1" fill="url(#bannerGradient)" stroke="#D97706" stroke-width="0.3"/>
  
  <!-- MQ Text -->
  <text x="12" y="14.5" font-family="Arial, sans-serif" font-size="6" font-weight="bold" text-anchor="middle" fill="#1E3A8A">MQ</text>
  
  <!-- Highlight dots on cylinder -->
  <circle cx="6" cy="6" r="0.5" fill="#6B9BD8" opacity="0.7"/>
  <circle cx="9" cy="6" r="0.5" fill="#6B9BD8" opacity="0.7"/>
  <circle cx="12" cy="6" r="0.5" fill="#6B9BD8" opacity="0.7"/>
</svg>
