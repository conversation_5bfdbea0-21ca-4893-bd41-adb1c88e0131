<?xml version="1.0" encoding="UTF-8"?>
<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- MQ Icon - Blue cylinder with yellow banner (similar to IBM MQ logo style) -->
  <defs>
    <linearGradient id="cylinderGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#1E3A8A;stop-opacity:1" />
      <stop offset="30%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="70%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E3A8A;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="bannerGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FCD34D;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F59E0B;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="topGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#60A5FA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3B82F6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Cylinder body -->
  <rect x="16" y="24" width="96" height="80" rx="8" ry="8" fill="url(#cylinderGradient)" stroke="#1E40AF" stroke-width="2"/>
  
  <!-- Cylinder top ellipse -->
  <ellipse cx="64" cy="24" rx="48" ry="12" fill="url(#topGradient)" stroke="#1E40AF" stroke-width="2"/>
  
  <!-- Yellow banner -->
  <rect x="20" y="52" width="88" height="24" rx="4" ry="4" fill="url(#bannerGradient)" stroke="#D97706" stroke-width="1"/>
  
  <!-- MQ Text -->
  <text x="64" y="70" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="#1E3A8A">MQ</text>
  
  <!-- Highlight dots on cylinder top -->
  <circle cx="40" cy="20" r="2" fill="#93C5FD" opacity="0.8"/>
  <circle cx="52" cy="18" r="2" fill="#93C5FD" opacity="0.8"/>
  <circle cx="64" cy="16" r="2" fill="#93C5FD" opacity="0.8"/>
  <circle cx="76" cy="18" r="2" fill="#93C5FD" opacity="0.8"/>
  <circle cx="88" cy="20" r="2" fill="#93C5FD" opacity="0.8"/>
  
  <!-- Side highlight -->
  <rect x="18" y="26" width="4" height="76" rx="2" ry="2" fill="#60A5FA" opacity="0.6"/>
</svg>
