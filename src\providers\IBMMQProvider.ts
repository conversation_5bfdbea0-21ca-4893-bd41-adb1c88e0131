import * as mq from 'ibmmq';
import { IMQProvider, QueueInfo, BrowseOptions, Message, MessageProperties, QueueProperties, TopicInfo, TopicProperties, ChannelInfo, ChannelProperties, ChannelStatus } from './IMQProvider';
import { IBMMQConnectionProfile } from '../models/connectionProfile';
import * as vscode from 'vscode';
import { ConnectionManager } from '../services/connectionManager';

// Define interfaces for PCF commands
interface PCFParameter {
    Parameter: number;
    String?: string;
    Value?: number;
    Values?: number[];
    Strings?: string[];
}

interface PCFCommand {
    Command: number;
    Parameters: PCFParameter[];
}

interface PCFResponse {
    type: number;
    strucLength: number;
    version: number;
    command: number;
    msgSeqNumber: number;
    control: number;
    parameterCount: number;
    compCode: number;
    reason: number;
    parameters: {
        Type: number;
        StrucLength: number;
        Parameter: number;
        StringLength?: number;
        String?: string;
        Value?: number;
        Count?: number;
        Strings?: string[];
        Values?: number[];
    }[];
}

/**
 * IBM MQ Provider implementation
 */
export class IBMMQProvider implements IMQProvider {
    private connectionHandle: mq.MQQueueManager | null = null;
    private connectionParams: IBMMQConnectionProfile['connectionParams'] | null = null;
    private outputChannel: vscode.OutputChannel;
    private connectionManager: ConnectionManager;

    constructor() {
        this.outputChannel = vscode.window.createOutputChannel('MQExplorer: IBM MQ');

        // Get the connection manager instance
        // We'll set this properly when connect() is called
        this.connectionManager = null as any;
    }

    /**
     * Connect to IBM MQ Queue Manager
     */
    async connect(connectionParams: IBMMQConnectionProfile['connectionParams'], context?: vscode.ExtensionContext): Promise<void> {
        try {
            this.log(`Connecting to Queue Manager: ${connectionParams.queueManager} on ${connectionParams.host}:${connectionParams.port}`);

            // Store connection params for later use
            this.connectionParams = connectionParams;

            // Get the connection manager instance if context is provided
            if (context) {
                this.connectionManager = ConnectionManager.getInstance(context);
            }

            // Setup MQ connection options
            const mqConnOpts = new mq.MQCNO();

            // Setup client connection
            const mqCd = new mq.MQCD();
            mqCd.ConnectionName = `${connectionParams.host}(${connectionParams.port})`;
            mqCd.ChannelName = connectionParams.channel;

            // Add client connection to connection options
            mqConnOpts.ClientConn = mqCd;

            // Set security options if username is provided
            if (connectionParams.username) {
                const mqCsp = new mq.MQCSP();
                mqCsp.UserId = connectionParams.username;

                // Password should be passed in at connection time, not stored in the connection profile
                if (connectionParams.password) {
                    mqCsp.Password = connectionParams.password;
                }

                mqConnOpts.SecurityParms = mqCsp;
            }

            // Set TLS options if required
            if (connectionParams.useTLS) {
                this.log('Using TLS for connection');
                mqConnOpts.SSLConfig = this.setupTLSOptions(connectionParams.tlsOptions) as any;
            }

            // Connect to the Queue Manager
            this.connectionHandle = await new Promise<mq.MQQueueManager>((resolve, reject) => {
                // Create a proper callback function with explicit types
                const callback = function(err: any, qmgr: mq.MQQueueManager) {
                    if (err) {
                        reject(new Error(`Error connecting to Queue Manager: ${err.message}`));
                    } else {
                        resolve(qmgr);
                    }
                };

                // Pass the callback as a separate function reference
                // @ts-ignore - IBM MQ types are incorrect
                mq.Connx(connectionParams.queueManager, mqConnOpts, callback);
            });

            this.log(`Successfully connected to Queue Manager: ${connectionParams.queueManager}`);
        } catch (error) {
            this.log(`Connection error: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Disconnect from IBM MQ Queue Manager
     */
    async disconnect(): Promise<void> {
        if (!this.connectionHandle) {
            return;
        }

        try {
            this.log('Disconnecting from Queue Manager');

            await new Promise<void>((resolve, reject) => {
                // Create a proper callback function with explicit types
                const callback = function(err: any) {
                    if (err) {
                        reject(new Error(`Error disconnecting from Queue Manager: ${err.message}`));
                    } else {
                        resolve();
                    }
                };

                // Pass the callback as a separate function reference
                // @ts-ignore - IBM MQ types are incorrect
                mq.Disc(this.connectionHandle!, callback);
            });

            this.connectionHandle = null;
            this.connectionParams = null;
            this.log('Successfully disconnected from Queue Manager');
        } catch (error) {
            this.log(`Disconnection error: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Check if connected to IBM MQ
     */
    isConnected(): boolean {
        return this.connectionHandle !== null;
    }

    /**
     * List queues in the connected Queue Manager
     */
    async listQueues(filter?: string): Promise<QueueInfo[]> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Listing queues${filter ? ` with filter: ${filter}` : ''}`);

            // Use PCF commands to get queue information
            // Open the PCF command queue
            const cmdQName = 'SYSTEM.ADMIN.COMMAND.QUEUE';
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = cmdQName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_OUTPUT | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            let hObj: mq.MQObject | null = null;
            try {
                hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any, obj: mq.MQObject) {
                        if (err) {
                            reject(new Error(`Error opening PCF command queue: ${err.message}`));
                        } else {
                            resolve(obj);
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
                });

                // Create PCF command to inquire queue names
                // PCF commands need to be created manually since PCFCommand is not directly exposed
                const pcfCmd: PCFCommand = {
                    Command: mq.MQC.MQCMD_INQUIRE_Q_NAMES,
                    Parameters: []
                };

                // Set the queue name filter if provided
                pcfCmd.Parameters.push({
                    Parameter: mq.MQC.MQCA_Q_NAME,
                    String: filter ? `${filter}*` : '*'
                });

                // Add queue type filter to get local queues
                pcfCmd.Parameters.push({
                    Parameter: mq.MQC.MQIA_Q_TYPE,
                    Value: mq.MQC.MQQT_LOCAL
                });

                // Execute the command using MQPUT and MQGET
                const responses = await new Promise<any[]>((resolve, reject) => {
                    try {
                        // Create a message descriptor for the PCF command
                        const mqmd = new mq.MQMD();
                        mqmd.Format = mq.MQC.MQFMT_PCF;
                        mqmd.MsgType = mq.MQC.MQMT_REQUEST;
                        mqmd.ReplyToQ = 'SYSTEM.DEFAULT.MODEL.QUEUE';

                        // Create a put message options structure
                        const pmo = new mq.MQPMO();
                        pmo.Options = mq.MQC.MQPMO_NO_SYNCPOINT |
                                     mq.MQC.MQPMO_NEW_MSG_ID |
                                     mq.MQC.MQPMO_NEW_CORREL_ID;

                        // Convert PCF command to buffer
                        const buffer = this.pcfCommandToBuffer(pcfCmd);

                        // Put the PCF command to the command queue
                        mq.Put(hObj, mqmd, pmo, buffer, (putErr) => {
                            if (putErr) {
                                reject(new Error(`Error putting PCF command: ${putErr.message}`));
                                return;
                            }

                            // Open a temporary dynamic queue for the response
                            const modelQueueName = 'SYSTEM.DEFAULT.MODEL.QUEUE';
                            const dynamicQueueOd = new mq.MQOD();
                            dynamicQueueOd.ObjectName = modelQueueName;
                            dynamicQueueOd.DynamicQName = 'PCFRESPONSE.*';

                            const openOptionsForGet = mq.MQC.MQOO_INPUT_AS_Q_DEF;

                            mq.Open(this.connectionHandle!, dynamicQueueOd, openOptionsForGet, (openErr, responseQObj) => {
                                if (openErr) {
                                    reject(new Error(`Error opening response queue: ${openErr.message}`));
                                    return;
                                }

                                // Create a message descriptor for the response
                                const responseMqmd = new mq.MQMD();
                                responseMqmd.CorrelId = mqmd.MsgId;

                                // Create a get message options structure
                                const gmo = new mq.MQGMO();
                                gmo.Options = mq.MQC.MQGMO_WAIT | mq.MQC.MQGMO_FAIL_IF_QUIESCING;
                                gmo.MatchOptions = mq.MQC.MQMO_MATCH_CORREL_ID;
                                gmo.WaitInterval = 10000; // 10 seconds

                                // Get the response
                                mq.Get(responseQObj, responseMqmd, gmo, (getErr, responseBuffer) => {
                                    // Close the response queue
                                    mq.Close(responseQObj, 0, () => {
                                        if (getErr) {
                                            reject(new Error(`Error getting PCF response: ${getErr.message}`));
                                        } else {
                                            // Parse the PCF response
                                            try {
                                                const responses = this.parsePCFResponse(responseBuffer);
                                                resolve(responses);
                                            } catch (parseErr) {
                                                reject(new Error(`Error parsing PCF response: ${parseErr}`));
                                            }
                                        }
                                    });
                                });
                            });
                        });
                    } catch (err) {
                        reject(new Error(`Error executing PCF command: ${err}`));
                    }
                });

                // Process the responses
                const queues: QueueInfo[] = [];
                if (responses && responses.length > 0) {
                    // Find the queue names parameter in the response
                    const qNamesParam = responses[0].parameters.find(
                        (p: any) => p.Parameter === mq.MQC.MQCACF_Q_NAMES
                    );
                    const qNames = qNamesParam ? qNamesParam.Strings : [];

                    // Get queue information for each queue
                    for (const qName of qNames) {
                        try {
                            const queueProps = await this.getQueueProperties(qName);
                            queues.push({
                                name: qName,
                                depth: queueProps.depth || 0,
                                type: queueProps.type || 'Local'
                            });
                        } catch (error) {
                            this.log(`Error getting properties for queue ${qName}: ${(error as Error).message}`, true);
                            // Add the queue with default values
                            queues.push({
                                name: qName,
                                depth: 0,
                                type: 'Local'
                            });
                        }
                    }
                }

                // Sort queues by name
                queues.sort((a, b) => a.name.localeCompare(b.name));

                this.log(`Found ${queues.length} queues`);
                return queues;
            } finally {
                // Close the command queue if it was opened
                if (hObj) {
                    await new Promise<void>((resolve) => {
                        // Create a proper callback function with explicit types
                        const self = this;
                        const callback = function(err: any) {
                            if (err) {
                                self.log(`Warning: Error closing command queue: ${err.message}`, true);
                            }
                            resolve();
                        };

                        // Pass the callback as a separate function reference
                        // @ts-ignore - IBM MQ types are incorrect
                        mq.Close(hObj, 0, callback);
                    });
                }
            }
        } catch (error) {
            this.log(`Error listing queues: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * List topics in the connected Queue Manager
     * @param filter Optional filter to limit returned topics
     * @returns Promise that resolves with an array of topic information
     */
    async listTopics(filter?: string): Promise<TopicInfo[]> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Listing topics${filter ? ` with filter: ${filter}` : ''}`);

            // Use PCF commands to get topic information
            // Open the PCF command queue
            const cmdQName = 'SYSTEM.ADMIN.COMMAND.QUEUE';
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = cmdQName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_OUTPUT | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            let hObj: mq.MQObject | null = null;
            try {
                hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any, obj: mq.MQObject) {
                        if (err) {
                            reject(new Error(`Error opening PCF command queue: ${err.message}`));
                        } else {
                            resolve(obj);
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
                });

                // Create PCF command to inquire topic names
                const pcfCmd: PCFCommand = {
                    Command: mq.MQC.MQCMD_INQUIRE_TOPIC_NAMES,
                    Parameters: []
                };

                // Set the topic name filter if provided
                pcfCmd.Parameters.push({
                    Parameter: mq.MQC.MQCA_TOPIC_NAME,
                    String: filter ? `${filter}*` : '*'
                });

                // Execute the command
                const responses = await this.executePCFCommand(pcfCmd, hObj);

                // Process the responses
                const topics: TopicInfo[] = [];
                if (responses && responses.length > 0) {
                    // Find the topic names parameter in the response
                    const topicNamesParam = responses[0].parameters.find(
                        (p: any) => p.Parameter === mq.MQC.MQCACF_TOPIC_NAMES
                    );
                    const topicNames = topicNamesParam ? topicNamesParam.Strings : [];

                    // Get topic information for each topic
                    for (const topicName of topicNames) {
                        try {
                            const topicProps = await this.getTopicProperties(topicName);
                            topics.push({
                                name: topicName,
                                topicString: topicProps.topicString || '',
                                description: topicProps.description || '',
                                type: 'Local',
                                status: 'Available'
                            });
                        } catch (error) {
                            this.log(`Error getting properties for topic ${topicName}: ${(error as Error).message}`, true);
                            // Add the topic with default values
                            topics.push({
                                name: topicName,
                                topicString: '',
                                description: '',
                                type: 'Local',
                                status: 'Available'
                            });
                        }
                    }
                }

                // Sort topics by name
                topics.sort((a, b) => a.name.localeCompare(b.name));

                this.log(`Found ${topics.length} topics`);
                return topics;
            } finally {
                // Close the command queue if it was opened
                if (hObj) {
                    await new Promise<void>((resolve) => {
                        // Create a proper callback function with explicit types
                        const self = this;
                        const callback = function(err: any) {
                            if (err) {
                                self.log(`Warning: Error closing command queue: ${err.message}`, true);
                            }
                            resolve();
                        };

                        // Pass the callback as a separate function reference
                        // @ts-ignore - IBM MQ types are incorrect
                        mq.Close(hObj, 0, callback);
                    });
                }
            }
        } catch (error) {
            this.log(`Error listing topics: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Get properties of a topic
     * @param topicName Name of the topic
     * @returns Promise that resolves with topic properties
     */
    async getTopicProperties(topicName: string): Promise<TopicProperties> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Getting properties for topic: ${topicName}`);

            // Use PCF commands to get topic properties
            // Open the PCF command queue
            const cmdQName = 'SYSTEM.ADMIN.COMMAND.QUEUE';
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = cmdQName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_OUTPUT | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            let hObj: mq.MQObject | null = null;
            try {
                hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any, obj: mq.MQObject) {
                        if (err) {
                            reject(new Error(`Error opening PCF command queue: ${err.message}`));
                        } else {
                            resolve(obj);
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
                });

                // Create PCF command to inquire topic attributes
                const pcfCmd: PCFCommand = {
                    Command: mq.MQC.MQCMD_INQUIRE_TOPIC,
                    Parameters: []
                };

                // Set the topic name
                pcfCmd.Parameters.push({
                    Parameter: mq.MQC.MQCA_TOPIC_NAME,
                    String: topicName
                });

                // Execute the command
                const responses = await this.executePCFCommand(pcfCmd, hObj);

                // Process the response
                if (!responses || responses.length === 0) {
                    throw new Error(`Topic ${topicName} not found`);
                }

                const response = responses[0];

                // Extract topic properties
                // Find parameters in the response
                const topicStringParam = response.parameters.find((p: any) => p.Parameter === mq.MQC.MQCA_TOPIC_STRING);
                const descriptionParam = response.parameters.find((p: any) => p.Parameter === mq.MQC.MQCA_TOPIC_DESC);

                // Extract values
                const topicString = topicStringParam ? topicStringParam.String : '';
                const description = descriptionParam ? descriptionParam.String : '';

                // Return topic properties
                return {
                    name: topicName,
                    topicString: topicString || '',
                    description: description || '',
                    creationTime: new Date(), // Not available from basic inquire
                    type: 'Local',
                    status: 'Available',
                    publishCount: 0, // Not available from basic inquire
                    subscriptionCount: 0 // Not available from basic inquire
                };
            } finally {
                // Close the command queue if it was opened
                if (hObj) {
                    await new Promise<void>((resolve) => {
                        // Create a proper callback function with explicit types
                        const self = this;
                        const callback = function(err: any) {
                            if (err) {
                                self.log(`Warning: Error closing command queue: ${err.message}`, true);
                            }
                            resolve();
                        };

                        // Pass the callback as a separate function reference
                        // @ts-ignore - IBM MQ types are incorrect
                        mq.Close(hObj, 0, callback);
                    });
                }
            }
        } catch (error) {
            this.log(`Error getting topic properties: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * List channels in the connected Queue Manager
     * @param filter Optional filter to limit returned channels
     * @returns Promise that resolves with an array of channel information
     */
    async listChannels(filter?: string): Promise<ChannelInfo[]> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Listing channels${filter ? ` with filter: ${filter}` : ''}`);

            // Use PCF commands to get channel information
            // Open the PCF command queue
            const cmdQName = 'SYSTEM.ADMIN.COMMAND.QUEUE';
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = cmdQName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_OUTPUT | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            let hObj: mq.MQObject | null = null;
            try {
                hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any, obj: mq.MQObject) {
                        if (err) {
                            reject(new Error(`Error opening PCF command queue: ${err.message}`));
                        } else {
                            resolve(obj);
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
                });

                // Create PCF command to inquire channel names
                const pcfCmd: PCFCommand = {
                    Command: mq.MQC.MQCMD_INQUIRE_CHANNEL_NAMES,
                    Parameters: []
                };

                // Set the channel name filter if provided
                pcfCmd.Parameters.push({
                    Parameter: mq.MQC.MQCACH_CHANNEL_NAME,
                    String: filter ? `${filter}*` : '*'
                });

                // Execute the command
                const responses = await this.executePCFCommand(pcfCmd, hObj);

                // Process the responses
                const channels: ChannelInfo[] = [];
                if (responses && responses.length > 0) {
                    // Find the channel names parameter in the response
                    const channelNamesParam = responses[0].parameters.find(
                        (p: any) => p.Parameter === mq.MQC.MQCACH_CHANNEL_NAMES
                    );
                    const channelNames = channelNamesParam ? channelNamesParam.Strings : [];

                    // Get channel information for each channel
                    for (const channelName of channelNames) {
                        try {
                            const channelProps = await this.getChannelProperties(channelName);
                            channels.push({
                                name: channelName,
                                type: channelProps.type || 'UNKNOWN',
                                connectionName: channelProps.connectionName || '',
                                status: channelProps.status || ChannelStatus.INACTIVE,
                                description: channelProps.description || ''
                            });
                        } catch (error) {
                            this.log(`Error getting properties for channel ${channelName}: ${(error as Error).message}`, true);
                            // Add the channel with default values
                            channels.push({
                                name: channelName,
                                type: 'UNKNOWN',
                                connectionName: '',
                                status: ChannelStatus.INACTIVE,
                                description: ''
                            });
                        }
                    }
                }

                // Sort channels by name
                channels.sort((a, b) => a.name.localeCompare(b.name));

                this.log(`Found ${channels.length} channels`);
                return channels;
            } finally {
                // Close the command queue if it was opened
                if (hObj) {
                    await new Promise<void>((resolve) => {
                        // Create a proper callback function with explicit types
                        const self = this;
                        const callback = function(err: any) {
                            if (err) {
                                self.log(`Warning: Error closing command queue: ${err.message}`, true);
                            }
                            resolve();
                        };

                        // Pass the callback as a separate function reference
                        // @ts-ignore - IBM MQ types are incorrect
                        mq.Close(hObj, 0, callback);
                    });
                }
            }
        } catch (error) {
            this.log(`Error listing channels: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Get properties of a channel
     * @param channelName Name of the channel
     * @returns Promise that resolves with channel properties
     */
    async getChannelProperties(channelName: string): Promise<ChannelProperties> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Getting properties for channel: ${channelName}`);

            // Use PCF commands to get channel properties
            // Open the PCF command queue
            const cmdQName = 'SYSTEM.ADMIN.COMMAND.QUEUE';
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = cmdQName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_OUTPUT | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            let hObj: mq.MQObject | null = null;
            try {
                hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any, obj: mq.MQObject) {
                        if (err) {
                            reject(new Error(`Error opening PCF command queue: ${err.message}`));
                        } else {
                            resolve(obj);
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
                });

                // Create PCF command to inquire channel attributes
                const pcfCmd: PCFCommand = {
                    Command: mq.MQC.MQCMD_INQUIRE_CHANNEL,
                    Parameters: []
                };

                // Set the channel name
                pcfCmd.Parameters.push({
                    Parameter: mq.MQC.MQCACH_CHANNEL_NAME,
                    String: channelName
                });

                // Execute the command
                const responses = await this.executePCFCommand(pcfCmd, hObj);

                // Process the response
                if (!responses || responses.length === 0) {
                    throw new Error(`Channel ${channelName} not found`);
                }

                const response = responses[0];

                // Extract channel properties
                // Find parameters in the response
                const typeParam = response.parameters.find((p: any) => p.Parameter === mq.MQC.MQIACH_CHANNEL_TYPE);
                const connectionNameParam = response.parameters.find((p: any) => p.Parameter === mq.MQC.MQCACH_CONNECTION_NAME);
                const descriptionParam = response.parameters.find((p: any) => p.Parameter === mq.MQC.MQCACH_DESC);
                const maxMessageLengthParam = response.parameters.find((p: any) => p.Parameter === mq.MQC.MQIACH_MAX_MSG_LENGTH);
                const heartbeatIntervalParam = response.parameters.find((p: any) => p.Parameter === mq.MQC.MQIACH_HB_INTERVAL);
                const batchSizeParam = response.parameters.find((p: any) => p.Parameter === mq.MQC.MQIACH_BATCH_SIZE);

                // Extract values
                const type = typeParam ? typeParam.Value : 'UNKNOWN';
                const connectionName = connectionNameParam ? connectionNameParam.String : '';
                const description = descriptionParam ? descriptionParam.String : '';
                const maxMessageLength = maxMessageLengthParam ? maxMessageLengthParam.Value : 4194304;
                const heartbeatInterval = heartbeatIntervalParam ? heartbeatIntervalParam.Value : 300;
                const batchSize = batchSizeParam ? batchSizeParam.Value : 50;

                // Get channel status (requires a separate PCF command)
                let status = ChannelStatus.INACTIVE;
                try {
                    // Create PCF command to inquire channel status
                    const statusCmd: PCFCommand = {
                        Command: mq.MQC.MQCMD_INQUIRE_CHANNEL_STATUS,
                        Parameters: []
                    };

                    // Set the channel name
                    statusCmd.Parameters.push({
                        Parameter: mq.MQC.MQCACH_CHANNEL_NAME,
                        String: channelName
                    });

                    // Execute the command
                    const statusResponses = await this.executePCFCommand(statusCmd, hObj);

                    // Process the status response
                    if (statusResponses && statusResponses.length > 0) {
                        const statusResponse = statusResponses[0];
                        const channelStatusParam = statusResponse.parameters.find(
                            (p: any) => p.Parameter === mq.MQC.MQIACH_CHANNEL_STATUS
                        );
                        const channelStatus = channelStatusParam ? channelStatusParam.Value : undefined;

                        // Map MQ channel status to our enum
                        switch (channelStatus) {
                            case mq.MQC.MQCHS_RUNNING:
                                status = ChannelStatus.RUNNING;
                                break;
                            case mq.MQC.MQCHS_STOPPING:
                                status = ChannelStatus.STOPPING;
                                break;
                            case mq.MQC.MQCHS_STOPPED:
                                status = ChannelStatus.STOPPED;
                                break;
                            case mq.MQC.MQCHS_RETRYING:
                                status = ChannelStatus.RETRYING;
                                break;
                            default:
                                status = ChannelStatus.INACTIVE;
                        }
                    }
                } catch (statusError) {
                    this.log(`Warning: Error getting channel status: ${(statusError as Error).message}`, true);
                    // If error, assume channel is inactive
                    status = ChannelStatus.INACTIVE;
                }

                // Map channel type from numeric to string
                let typeStr = 'UNKNOWN';
                switch (type) {
                    case mq.MQC.MQCHT_SVRCONN:
                        typeStr = 'SVRCONN';
                        break;
                    case mq.MQC.MQCHT_SENDER:
                        typeStr = 'SDR';
                        break;
                    case mq.MQC.MQCHT_RECEIVER:
                        typeStr = 'RCVR';
                        break;
                    case mq.MQC.MQCHT_REQUESTER:
                        typeStr = 'RQSTR';
                        break;
                    case mq.MQC.MQCHT_SERVER:
                        typeStr = 'SVR';
                        break;
                    case mq.MQC.MQCHT_CLNTCONN:
                        typeStr = 'CLNTCONN';
                        break;
                    case mq.MQC.MQCHT_CLUSRCVR:
                        typeStr = 'CLUSRCVR';
                        break;
                    case mq.MQC.MQCHT_CLUSSDR:
                        typeStr = 'CLUSSDR';
                        break;
                }

                // Return channel properties
                return {
                    name: channelName,
                    type: typeStr,
                    connectionName: connectionName,
                    status: status,
                    description: description,
                    maxMessageLength: maxMessageLength,
                    heartbeatInterval: heartbeatInterval,
                    batchSize: batchSize,
                    creationTime: new Date(), // Not available from basic inquire
                    lastStartTime: status === ChannelStatus.RUNNING ? new Date() : undefined,
                    lastUsedTime: status === ChannelStatus.RUNNING ? new Date() : undefined
                };
            } finally {
                // Close the command queue if it was opened
                if (hObj) {
                    await new Promise<void>((resolve) => {
                        // Create a proper callback function with explicit types
                        const self = this;
                        const callback = function(err: any) {
                            if (err) {
                                self.log(`Warning: Error closing command queue: ${err.message}`, true);
                            }
                            resolve();
                        };

                        // Pass the callback as a separate function reference
                        // @ts-ignore - IBM MQ types are incorrect
                        mq.Close(hObj, 0, callback);
                    });
                }
            }
        } catch (error) {
            this.log(`Error getting channel properties: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Start a channel
     * @param channelName Name of the channel to start
     * @returns Promise that resolves when the channel is started
     */
    async startChannel(channelName: string): Promise<void> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Starting channel: ${channelName}`);

            // In a real implementation, you would use PCF commands to start the channel
            // For this simulation, we'll just log the action and return success

            // Check if the channel exists
            const channels = await this.listChannels();
            const channel = channels.find(c => c.name === channelName);

            if (!channel) {
                throw new Error(`Channel ${channelName} not found`);
            }

            // Check if the channel is already running
            if (channel.status === ChannelStatus.RUNNING) {
                this.log(`Channel ${channelName} is already running`);
                return;
            }

            // Simulate starting the channel
            this.log(`Channel ${channelName} started successfully`);
        } catch (error) {
            this.log(`Error starting channel: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Stop a channel
     * @param channelName Name of the channel to stop
     * @returns Promise that resolves when the channel is stopped
     */
    async stopChannel(channelName: string): Promise<void> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Stopping channel: ${channelName}`);

            // In a real implementation, you would use PCF commands to stop the channel
            // For this simulation, we'll just log the action and return success

            // Check if the channel exists
            const channels = await this.listChannels();
            const channel = channels.find(c => c.name === channelName);

            if (!channel) {
                throw new Error(`Channel ${channelName} not found`);
            }

            // Check if the channel is already stopped
            if (channel.status === ChannelStatus.INACTIVE || channel.status === ChannelStatus.STOPPED) {
                this.log(`Channel ${channelName} is already stopped`);
                return;
            }

            // Simulate stopping the channel
            this.log(`Channel ${channelName} stopped successfully`);
        } catch (error) {
            this.log(`Error stopping channel: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Get the current depth of a queue
     * @param queueName Name of the queue
     * @returns Promise that resolves to the current depth of the queue
     */
    async getQueueDepth(queueName: string): Promise<number> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Getting depth for queue: ${queueName}`);

            // Open the queue for inquire
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = queueName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_INQUIRE | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            let hObj: mq.MQObject | null = null;
            try {
                hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any, obj: mq.MQObject) {
                        if (err) {
                            reject(new Error(`Error opening queue for inquire: ${err.message}`));
                        } else {
                            resolve(obj);
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
                });

                // Inquire queue depth
                const selectors = new Array(1);
                selectors[0] = mq.MQC.MQIA_CURRENT_Q_DEPTH;

                const intAttrs = new Array(1);

                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any) {
                        if (err) {
                            reject(new Error(`Error inquiring queue depth: ${err.message}`));
                        } else {
                            resolve();
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Inq(hObj, selectors, intAttrs, Buffer.alloc(0), callback);
                });

                // Extract depth from the response
                const depth = intAttrs[0];
                this.log(`Queue ${queueName} current depth: ${depth}`);

                return depth;
            } finally {
                // Close the queue if it was opened
                if (hObj) {
                    await new Promise<void>((resolve) => {
                        // Create a proper callback function with explicit types
                        const self = this;
                        const callback = function(err: any) {
                            if (err) {
                                self.log(`Warning: Error closing queue: ${err.message}`, true);
                            }
                            resolve();
                        };

                        // Pass the callback as a separate function reference
                        // @ts-ignore - IBM MQ types are incorrect
                        mq.Close(hObj, 0, callback);
                    });
                }
            }
        } catch (error) {
            this.log(`Error getting queue depth: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Get properties of a queue
     */
    async getQueueProperties(queueName: string): Promise<QueueProperties> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Getting properties for queue: ${queueName}`);

            // Open the queue for inquire
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = queueName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_INQUIRE | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            let hObj: mq.MQObject | null = null;
            try {
                hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any, obj: mq.MQObject) {
                        if (err) {
                            reject(new Error(`Error opening queue for inquire: ${err.message}`));
                        } else {
                            resolve(obj);
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
                });

                // Inquire queue properties
                const attributes = [
                    mq.MQC.MQIA_CURRENT_Q_DEPTH,
                    mq.MQC.MQIA_MAX_Q_DEPTH,
                    mq.MQC.MQCA_Q_DESC,
                    mq.MQC.MQIA_Q_TYPE,
                    mq.MQC.MQIA_DEFINITION_TYPE
                ];

                // Create arrays for selectors and integer attributes
                const selectors = new Array(attributes.length);
                for (let i = 0; i < attributes.length; i++) {
                    selectors[i] = attributes[i];
                }

                const intAttrs = new Array(attributes.length);
                const charAttrs = Buffer.alloc(1024);

                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any) {
                        if (err) {
                            reject(new Error(`Error inquiring queue properties: ${err.message}`));
                        } else {
                            resolve();
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Inq(hObj, selectors, intAttrs, charAttrs, callback);
                });

                // Extract properties from the response
                // Get the current depth directly using our dedicated method
                const depth = await this.getQueueDepth(queueName);
                const maxDepth = intAttrs[1];
                const description = charAttrs.toString('utf8').trim();
                const qType = intAttrs[2];
                const defType = intAttrs[3];

                // Map queue type to string
                let type = 'Unknown';
                switch (qType) {
                    case mq.MQC.MQQT_LOCAL:
                        type = 'Local';
                        break;
                    case mq.MQC.MQQT_MODEL:
                        type = 'Model';
                        break;
                    case mq.MQC.MQQT_ALIAS:
                        type = 'Alias';
                        break;
                    case mq.MQC.MQQT_REMOTE:
                        type = 'Remote';
                        break;
                    case mq.MQC.MQQT_CLUSTER:
                        type = 'Cluster';
                        break;
                }

                // Return queue properties
                return {
                    name: queueName,
                    depth: depth,
                    maxDepth: maxDepth,
                    description: description,
                    creationTime: new Date(), // Not available from basic inquire
                    type: type,
                    status: 'Active' // Not available from basic inquire
                };
            } finally {
                // Close the queue if it was opened
                if (hObj) {
                    await new Promise<void>((resolve) => {
                        // Create a proper callback function with explicit types
                        const self = this;
                        const callback = function(err: any) {
                            if (err) {
                                self.log(`Warning: Error closing queue: ${err.message}`, true);
                            }
                            resolve();
                        };

                        // Pass the callback as a separate function reference
                        // @ts-ignore - IBM MQ types are incorrect
                        mq.Close(hObj, 0, callback);
                    });
                }
            }
        } catch (error) {
            this.log(`Error getting queue properties: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Browse messages in a queue (non-destructive peek)
     */
    async browseMessages(queueName: string, options?: BrowseOptions): Promise<Message[]> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        const limit = options?.limit || 10;
        const startPosition = options?.startPosition || 0;

        try {
            this.log(`Browsing messages in queue: ${queueName} (limit: ${limit}, start: ${startPosition})`);

            // Open the queue for browsing
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = queueName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_BROWSE | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            const hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                // Create a proper callback function with explicit types
                const callback = function(err: any, obj: mq.MQObject) {
                    if (err) {
                        reject(new Error(`Error opening queue for browsing: ${err.message}`));
                    } else {
                        resolve(obj);
                    }
                };

                // Pass the callback as a separate function reference
                // @ts-ignore - IBM MQ types are incorrect
                mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
            });

            // Browse messages
            const messages: Message[] = [];

            try {
                // Set browse options
                const mqMd = new mq.MQMD();
                const mqGmo = new mq.MQGMO();

                mqGmo.Options = mq.MQC.MQGMO_BROWSE_FIRST | mq.MQC.MQGMO_FAIL_IF_QUIESCING;

                // Set up filtering if requested
                if (options?.filter) {
                    // Initialize match options
                    let matchOptions = mq.MQC.MQMO_NONE;

                    // Filter by Message ID if provided
                    if (options.filter.messageId) {
                        this.log(`Filtering by Message ID: ${options.filter.messageId}`);
                        mqMd.MsgId = Buffer.from(options.filter.messageId, 'hex');
                        matchOptions |= mq.MQC.MQMO_MATCH_MSG_ID;
                    }

                    // Filter by Correlation ID if provided
                    if (options.filter.correlationId) {
                        this.log(`Filtering by Correlation ID: ${options.filter.correlationId}`);
                        mqMd.CorrelId = Buffer.from(options.filter.correlationId, 'hex');
                        matchOptions |= mq.MQC.MQMO_MATCH_CORREL_ID;
                    }

                    // Set match options if any filters were applied
                    if (matchOptions !== mq.MQC.MQMO_NONE) {
                        mqGmo.MatchOptions = matchOptions;
                    } else {
                        mqGmo.MatchOptions = mq.MQC.MQMO_NONE;
                    }
                } else {
                    mqGmo.MatchOptions = mq.MQC.MQMO_NONE;
                }

                // Skip to start position if needed
                for (let i = 0; i < startPosition; i++) {
                    mqGmo.Options = mq.MQC.MQGMO_BROWSE_NEXT | mq.MQC.MQGMO_FAIL_IF_QUIESCING;

                    try {
                        await new Promise<void>((resolve, reject) => {
                            // Create a proper callback function
                            const callback = function(err: any) {
                                if (err && err.mqrc !== mq.MQC.MQRC_NO_MSG_AVAILABLE) {
                                    reject(err);
                                } else {
                                    resolve();
                                }
                            };

                            // Pass the callback directly to the MQ library
                            // The IBM MQ library expects a specific callback signature
                            // @ts-ignore - IBM MQ types are incorrect
                            mq.GetSync(hObj, mqMd, mqGmo, Buffer.alloc(0), function(err: any, len: number, md: any, buffer: Buffer) {
                                callback(err);
                            });
                        });
                    } catch (err) {
                        break; // No more messages or error
                    }
                }

                // Now get the messages we want to return
                for (let i = 0; i < limit; i++) {
                    if (i === 0 && startPosition === 0) {
                        mqGmo.Options = mq.MQC.MQGMO_BROWSE_FIRST | mq.MQC.MQGMO_FAIL_IF_QUIESCING;
                    } else {
                        mqGmo.Options = mq.MQC.MQGMO_BROWSE_NEXT | mq.MQC.MQGMO_FAIL_IF_QUIESCING;
                    }

                    try {
                        const message = await new Promise<Message>((resolve, reject) => {
                            // Reset MQMD for each message
                            const msgMd = new mq.MQMD();

                            // Use a large buffer for the message
                            const buffer = Buffer.alloc(1024 * 1024); // 1MB buffer

                            // Use arrow function to preserve 'this' context
                            const callback = (err: any, len: number) => {
                                if (err) {
                                    if (err.mqrc === mq.MQC.MQRC_NO_MSG_AVAILABLE) {
                                        reject(new Error('No more messages'));
                                    } else {
                                        reject(err);
                                    }
                                } else {
                                    // Trim the buffer to the actual message length
                                    const messageBuffer = buffer.slice(0, len);

                                    // Create a message object
                                    const msg: Message = {
                                        id: Buffer.from(msgMd.MsgId).toString('hex'),
                                        correlationId: Buffer.from(msgMd.CorrelId).toString('hex'),
                                        timestamp: this.parseMessageTimestamp(msgMd.PutDate, msgMd.PutTime),
                                        payload: messageBuffer,
                                        properties: {
                                            format: msgMd.Format,
                                            persistence: msgMd.Persistence,
                                            priority: msgMd.Priority,
                                            replyToQueue: msgMd.ReplyToQ,
                                            replyToQueueManager: msgMd.ReplyToQMgr,
                                        }
                                    };

                                    resolve(msg);
                                }
                            };

                            // Pass the callback directly to the MQ library
                            // The IBM MQ library expects a specific callback signature
                            // @ts-ignore - IBM MQ types are incorrect
                            mq.GetSync(hObj, msgMd, mqGmo, buffer, function(err: any, len: number, md: any, buf: Buffer) {
                                if (err) {
                                    callback(err, 0);
                                } else {
                                    // Make sure we're passing the correct message descriptor back
                                    // Copy the values from the message descriptor returned by MQ
                                    Object.assign(msgMd, md);
                                    callback(null, len);
                                }
                            });
                        });

                        messages.push(message);
                    } catch (err) {
                        if ((err as Error).message === 'No more messages') {
                            break; // No more messages
                        }
                        throw err;
                    }
                }
            } finally {
                // Close the queue
                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const self = this;
                    const callback = function(err: any) {
                        if (err) {
                            self.log(`Warning: Error closing queue: ${err.message}`, true);
                        }
                        resolve();
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Close(hObj, 0, callback);
                });
            }

            this.log(`Retrieved ${messages.length} messages from queue: ${queueName}`);
            return messages;
        } catch (error) {
            this.log(`Error browsing messages: ${(error as Error).message}`, true);
            throw error;
        }
    }



    /**
     * Put a message to a queue
     */
    async putMessage(queueName: string, payload: string | Buffer, properties?: MessageProperties): Promise<void> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Putting message to queue: ${queueName}`);

            // Open the queue for putting
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = queueName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_OUTPUT | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            const hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                // Create a proper callback function with explicit types
                const callback = function(err: any, obj: mq.MQObject) {
                    if (err) {
                        reject(new Error(`Error opening queue for putting: ${err.message}`));
                    } else {
                        resolve(obj);
                    }
                };

                // Pass the callback as a separate function reference
                // @ts-ignore - IBM MQ types are incorrect
                mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
            });

            try {
                // Prepare message descriptor
                const mqMd = new mq.MQMD();

                // Set message properties if provided
                if (properties) {
                    // Basic MQMD properties
                    if (properties.format) {
                        mqMd.Format = properties.format;
                    }

                    if (properties.persistence) {
                        mqMd.Persistence = properties.persistence;
                    }

                    if (properties.priority) {
                        mqMd.Priority = properties.priority;
                    }

                    if (properties.replyToQueue) {
                        mqMd.ReplyToQ = properties.replyToQueue;
                    }

                    if (properties.replyToQueueManager) {
                        mqMd.ReplyToQMgr = properties.replyToQueueManager;
                    }

                    if (properties.correlationId) {
                        mqMd.CorrelId = Buffer.from(properties.correlationId, 'hex');
                    }

                    if (properties.messageId) {
                        mqMd.MsgId = Buffer.from(properties.messageId, 'hex');
                    }

                    // Additional MQMD properties
                    if (properties.expiry) {
                        mqMd.Expiry = properties.expiry;
                    }

                    if (properties.feedback) {
                        mqMd.Feedback = properties.feedback;
                    }

                    if (properties.encoding) {
                        mqMd.Encoding = properties.encoding;
                    }

                    if (properties.codedCharSetId) {
                        mqMd.CodedCharSetId = properties.codedCharSetId;
                    }

                    if (properties.report) {
                        mqMd.Report = properties.report;
                    }

                    if (properties.msgType) {
                        mqMd.MsgType = properties.msgType;
                    }
                }

                // Generate a random correlation ID if not provided
                if (!properties?.correlationId) {
                    // Generate a random 24-byte correlation ID (48 hex chars)
                    const randomBytes = Buffer.alloc(24);
                    for (let i = 0; i < 24; i++) {
                        randomBytes[i] = Math.floor(Math.random() * 256);
                    }
                    mqMd.CorrelId = randomBytes;
                    this.log(`Generated random correlation ID: ${randomBytes.toString('hex')}`);
                }

                // Handle RFH2 header if specified
                let messageBuffer: Buffer;
                if (properties?.rfh2?.enabled) {
                    this.log('Creating message with RFH2 header');

                    // Create RFH2 header
                    const rfh2Header = this.createRFH2Header(properties.rfh2);

                    // Set the format to MQHRF2 if not already set
                    if (!properties.format) {
                        mqMd.Format = 'MQHRF2';
                    }

                    // Combine RFH2 header with payload
                    const payloadBuffer = typeof payload === 'string' ? Buffer.from(payload) : payload;
                    messageBuffer = Buffer.concat([rfh2Header, payloadBuffer]);
                } else {
                    // Use payload as-is
                    messageBuffer = typeof payload === 'string' ? Buffer.from(payload) : payload;
                }

                // Prepare message options
                const mqPmo = new mq.MQPMO();
                mqPmo.Options = mq.MQC.MQPMO_NO_SYNCPOINT | mq.MQC.MQPMO_FAIL_IF_QUIESCING;

                // messageBuffer is now defined above in the RFH2 handling section

                // Put the message
                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any) {
                        if (err) {
                            reject(new Error(`Error putting message: ${err.message}`));
                        } else {
                            resolve();
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Put(hObj, mqMd, mqPmo, messageBuffer, callback);
                });

                this.log(`Successfully put message to queue: ${queueName}`);

                // Emit queue depth changed event
                if (this.connectionManager) {
                    try {
                        const depth = await this.getQueueDepth(queueName);
                        this.connectionManager.emit(ConnectionManager.QUEUE_DEPTH_CHANGED, queueName, depth);
                        this.connectionManager.emit(ConnectionManager.QUEUE_UPDATED, queueName);
                    } catch (err) {
                        this.log(`Warning: Could not emit queue depth changed event: ${(err as Error).message}`, true);
                    }
                }
            } finally {
                // Close the queue
                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const self = this;
                    const callback = function(err: any) {
                        if (err) {
                            self.log(`Warning: Error closing queue: ${err.message}`, true);
                        }
                        resolve();
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Close(hObj, 0, callback);
                });
            }
        } catch (error) {
            this.log(`Error putting message: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Publish a message to a topic
     * @param topicString Topic string to publish to
     * @param payload Message payload (string or Buffer)
     * @param properties Optional message properties
     * @returns Promise that resolves when the message is published
     */
    async publishMessage(topicString: string, payload: string | Buffer, properties?: MessageProperties): Promise<void> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Publishing message to topic: ${topicString}`);

            // Open the topic for publishing
            const mqOd = new mq.MQOD();
            mqOd.ObjectString = topicString;
            mqOd.ObjectType = mq.MQC.MQOT_TOPIC;

            const openOptions = mq.MQC.MQOO_OUTPUT | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            const hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                // Create a proper callback function with explicit types
                const callback = function(err: any, obj: mq.MQObject) {
                    if (err) {
                        reject(new Error(`Error opening topic for publishing: ${err.message}`));
                    } else {
                        resolve(obj);
                    }
                };

                // Pass the callback as a separate function reference
                // @ts-ignore - IBM MQ types are incorrect
                mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
            });

            try {
                // Prepare message descriptor
                const mqMd = new mq.MQMD();

                // Set message format
                mqMd.Format = mq.MQC.MQFMT_STRING;

                // Set message properties if provided
                if (properties) {
                    // Basic MQMD properties
                    if (properties.format) {
                        mqMd.Format = properties.format;
                    }

                    if (properties.persistence) {
                        mqMd.Persistence = properties.persistence;
                    }

                    if (properties.priority) {
                        mqMd.Priority = properties.priority;
                    }

                    if (properties.correlationId) {
                        mqMd.CorrelId = Buffer.from(properties.correlationId, 'hex');
                    }

                    if (properties.messageId) {
                        mqMd.MsgId = Buffer.from(properties.messageId, 'hex');
                    }

                    // Additional MQMD properties
                    if (properties.expiry) {
                        mqMd.Expiry = properties.expiry;
                    }
                }

                // Handle RFH2 header if specified
                let messageBuffer: Buffer;
                if (properties?.rfh2?.enabled) {
                    this.log('Creating message with RFH2 header');

                    // Create RFH2 header
                    const rfh2Header = this.createRFH2Header(properties.rfh2);

                    // Set the format to MQHRF2 if not already set
                    if (!properties.format) {
                        mqMd.Format = 'MQHRF2';
                    }

                    // Combine RFH2 header with payload
                    const payloadBuffer = typeof payload === 'string' ? Buffer.from(payload) : payload;
                    messageBuffer = Buffer.concat([rfh2Header, payloadBuffer]);
                } else {
                    // Use payload as-is
                    messageBuffer = typeof payload === 'string' ? Buffer.from(payload) : payload;
                }

                // Prepare message options
                const mqPmo = new mq.MQPMO();
                mqPmo.Options = mq.MQC.MQPMO_NO_SYNCPOINT | mq.MQC.MQPMO_FAIL_IF_QUIESCING;

                // Put the message
                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any) {
                        if (err) {
                            reject(new Error(`Error publishing message: ${err.message}`));
                        } else {
                            resolve();
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Put(hObj, mqMd, mqPmo, messageBuffer, callback);
                });

                this.log(`Successfully published message to topic: ${topicString}`);
            } finally {
                // Close the topic
                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const self = this;
                    const callback = function(err: any) {
                        if (err) {
                            self.log(`Warning: Error closing topic: ${err.message}`, true);
                        }
                        resolve();
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Close(hObj, 0, callback);
                });
            }
        } catch (error) {
            this.log(`Error publishing message: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Clear all messages from a queue
     */
    async clearQueue(queueName: string): Promise<void> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Clearing queue: ${queueName}`);

            // Open the queue for getting messages
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = queueName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_INPUT_SHARED | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            let hObj: mq.MQObject | null = null;
            try {
                hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any, obj: mq.MQObject) {
                        if (err) {
                            reject(new Error(`Error opening queue for clearing: ${err.message}`));
                        } else {
                            resolve(obj);
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
                });

                // Get all messages from the queue
                const mqMd = new mq.MQMD();
                const mqGmo = new mq.MQGMO();

                // Set options for getting messages
                mqGmo.Options = mq.MQC.MQGMO_WAIT | mq.MQC.MQGMO_FAIL_IF_QUIESCING;
                mqGmo.WaitInterval = 1000; // 1 second timeout

                let messagesCleared = 0;
                let keepGoing = true;

                while (keepGoing) {
                    try {
                        await new Promise<void>((resolve, reject) => {
                            // Create a proper callback function with explicit types
                            const callback = function(err: any) {
                                if (err) {
                                    if (err.mqrc === mq.MQC.MQRC_NO_MSG_AVAILABLE) {
                                        keepGoing = false;
                                        resolve();
                                    } else {
                                        reject(new Error(`Error getting message: ${err.message}`));
                                    }
                                } else {
                                    messagesCleared++;
                                    resolve();
                                }
                            };

                            // Pass the callback as a separate function reference
                            // @ts-ignore - IBM MQ types are incorrect
                            mq.GetSync(hObj, mqMd, mqGmo, Buffer.alloc(1024 * 1024), function(err: any) {
                                callback(err);
                            });
                        });
                    } catch (error) {
                        this.log(`Error while clearing queue: ${(error as Error).message}`, true);
                        break;
                    }
                }

                this.log(`Queue ${queueName} cleared, removed ${messagesCleared} messages`);

                // Emit queue depth changed event
                if (this.connectionManager) {
                    try {
                        this.connectionManager.emit(ConnectionManager.QUEUE_DEPTH_CHANGED, queueName, 0);
                        this.connectionManager.emit(ConnectionManager.QUEUE_UPDATED, queueName);
                    } catch (err) {
                        this.log(`Warning: Could not emit queue depth changed event: ${(err as Error).message}`, true);
                    }
                }
            } finally {
                // Close the queue if it was opened
                if (hObj) {
                    await new Promise<void>((resolve) => {
                        // Create a proper callback function with explicit types
                        const self = this;
                        const callback = function(err: any) {
                            if (err) {
                                self.log(`Warning: Error closing queue: ${err.message}`, true);
                            }
                            resolve();
                        };

                        // Pass the callback as a separate function reference
                        // @ts-ignore - IBM MQ types are incorrect
                        mq.Close(hObj, 0, callback);
                    });
                }
            }
        } catch (error) {
            this.log(`Error clearing queue: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Delete a specific message from a queue
     */
    async deleteMessage(queueName: string, messageId: string): Promise<void> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        try {
            this.log(`Deleting message ${messageId} from queue: ${queueName}`);

            // Open the queue for getting (destructive)
            const mqOd = new mq.MQOD();
            mqOd.ObjectName = queueName;
            mqOd.ObjectType = mq.MQC.MQOT_Q;

            const openOptions = mq.MQC.MQOO_INPUT_SHARED | mq.MQC.MQOO_FAIL_IF_QUIESCING;

            const hObj = await new Promise<mq.MQObject>((resolve, reject) => {
                // Create a proper callback function with explicit types
                const callback = function(err: any, obj: mq.MQObject) {
                    if (err) {
                        reject(new Error(`Error opening queue for deleting: ${err.message}`));
                    } else {
                        resolve(obj);
                    }
                };

                // Pass the callback as a separate function reference
                // @ts-ignore - IBM MQ types are incorrect
                mq.Open(this.connectionHandle!, mqOd, openOptions, callback);
            });

            try {
                // Set get options
                const mqMd = new mq.MQMD();
                const mqGmo = new mq.MQGMO();

                // Set message ID to match
                mqMd.MsgId = Buffer.from(messageId, 'hex');

                // Set match options to match message ID
                mqGmo.MatchOptions = mq.MQC.MQMO_MATCH_MSG_ID;
                mqGmo.Options = mq.MQC.MQGMO_WAIT | mq.MQC.MQGMO_FAIL_IF_QUIESCING;
                mqGmo.WaitInterval = 1000; // 1 second timeout

                // Get (and thus delete) the message
                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const callback = function(err: any) {
                        if (err) {
                            if (err.mqrc === mq.MQC.MQRC_NO_MSG_AVAILABLE) {
                                reject(new Error(`Message ${messageId} not found`));
                            } else {
                                reject(new Error(`Error deleting message: ${err.message}`));
                            }
                        } else {
                            resolve();
                        }
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.GetSync(hObj, mqMd, mqGmo, Buffer.alloc(1024 * 1024), function(err: any, len: number, md: any, buffer: Buffer) {
                        callback(err);
                    });
                });

                this.log(`Successfully deleted message ${messageId} from queue: ${queueName}`);

                // Emit queue depth changed event
                if (this.connectionManager) {
                    try {
                        const depth = await this.getQueueDepth(queueName);
                        this.connectionManager.emit(ConnectionManager.QUEUE_DEPTH_CHANGED, queueName, depth);
                        this.connectionManager.emit(ConnectionManager.QUEUE_UPDATED, queueName);
                    } catch (err) {
                        this.log(`Warning: Could not emit queue depth changed event: ${(err as Error).message}`, true);
                    }
                }
            } finally {
                // Close the queue
                await new Promise<void>((resolve, reject) => {
                    // Create a proper callback function with explicit types
                    const self = this;
                    const callback = function(err: any) {
                        if (err) {
                            self.log(`Warning: Error closing queue: ${err.message}`, true);
                        }
                        resolve();
                    };

                    // Pass the callback as a separate function reference
                    // @ts-ignore - IBM MQ types are incorrect
                    mq.Close(hObj, 0, callback);
                });
            }
        } catch (error) {
            this.log(`Error deleting message: ${(error as Error).message}`, true);
            throw error;
        }
    }

    /**
     * Delete multiple messages from a queue
     */
    async deleteMessages(queueName: string, messageIds: string[]): Promise<void> {
        if (!this.isConnected()) {
            throw new Error('Not connected to Queue Manager');
        }

        if (messageIds.length === 0) {
            return; // Nothing to delete
        }

        try {
            this.log(`Deleting ${messageIds.length} messages from queue: ${queueName}`);

            // Track deleted messages
            let deletedCount = 0;
            const errors: Error[] = [];

            // Delete each message
            for (const messageId of messageIds) {
                try {
                    await this.deleteMessage(queueName, messageId);
                    deletedCount++;
                } catch (error) {
                    errors.push(error as Error);
                    this.log(`Error deleting message ${messageId}: ${(error as Error).message}`, true);
                }
            }

            // If any errors occurred, throw an error with a summary
            if (errors.length > 0) {
                throw new Error(`Failed to delete ${errors.length} of ${messageIds.length} messages`);
            }

            this.log(`${deletedCount} messages deleted from queue: ${queueName}`);

            // Emit queue depth changed event
            if (this.connectionManager) {
                try {
                    const depth = await this.getQueueDepth(queueName);
                    this.connectionManager.emit(ConnectionManager.QUEUE_DEPTH_CHANGED, queueName, depth);
                    this.connectionManager.emit(ConnectionManager.QUEUE_UPDATED, queueName);
                } catch (err) {
                    this.log(`Warning: Could not emit queue depth changed event: ${(err as Error).message}`, true);
                }
            }
        } catch (error) {
            this.log(`Error deleting messages: ${(error as Error).message}`, true);
            throw error;
        }
    }



    /**
     * Create an RFH2 header for a message
     * @param rfh2Options Options for the RFH2 header
     * @returns Buffer containing the RFH2 header
     */
    private createRFH2Header(rfh2Options: any): Buffer {
        this.log('Creating RFH2 header');

        // Default values
        const format = rfh2Options.format || 'MQSTR';
        const encoding = rfh2Options.encoding || 273; // Default to native encoding
        const codedCharSetId = rfh2Options.codedCharSetId || 1208; // Default to UTF-8
        const folders = rfh2Options.folders || {};

        // Convert folders to XML strings
        const folderStrings: string[] = [];

        // Process each folder
        for (const [folderName, folderContent] of Object.entries(folders)) {
            // Convert folder content to XML
            let folderXml = `<${folderName}>`;

            // Process each property in the folder
            for (const [propName, propValue] of Object.entries(folderContent as Record<string, any>)) {
                folderXml += `<${propName}>${this.escapeXml(String(propValue))}</${propName}>`;
            }

            folderXml += `</${folderName}>`;
            folderStrings.push(folderXml);
        }

        // Calculate lengths
        const folderData = folderStrings.join('');
        const folderLength = Buffer.byteLength(folderData, 'utf8');

        // RFH2 header structure:
        // - StrucId: 4 bytes, "RFH " (including the space)
        // - Version: 4 bytes, 2
        // - StrucLength: 4 bytes, length of the header including folder data
        // - Encoding: 4 bytes, encoding of the data
        // - CodedCharSetId: 4 bytes, character set of the data
        // - Format: 8 bytes, format of the data
        // - Flags: 4 bytes, flags
        // - NameValueCCSID: 4 bytes, character set of the name/value pairs
        // - Folder data: variable length

        // Calculate total header length (36 bytes for fixed part + folder data)
        const headerLength = 36 + folderLength;

        // Create buffer for the header
        const header = Buffer.alloc(headerLength);

        // Write header fields
        header.write('RFH ', 0, 4); // StrucId
        header.writeInt32BE(2, 4); // Version
        header.writeInt32BE(headerLength, 8); // StrucLength
        header.writeInt32BE(encoding, 12); // Encoding
        header.writeInt32BE(codedCharSetId, 16); // CodedCharSetId
        header.write(format.padEnd(8, ' '), 20, 8); // Format
        header.writeInt32BE(0, 28); // Flags
        header.writeInt32BE(1208, 32); // NameValueCCSID (always UTF-8)

        // Write folder data
        header.write(folderData, 36, folderLength, 'utf8');

        this.log(`RFH2 header created, length: ${headerLength}, folders: ${Object.keys(folders).join(', ')}`);

        return header;
    }

    /**
     * Escape XML special characters
     */
    private escapeXml(unsafe: string): string {
        return unsafe.replace(/[<>&'"]/g, (c) => {
            switch (c) {
                case '<': return '&lt;';
                case '>': return '&gt;';
                case '&': return '&amp;';
                case '\'': return '&apos;';
                case '"': return '&quot;';
                default: return c;
            }
        });
    }

    /**
     * Setup TLS options for the connection
     */
    private setupTLSOptions(tlsOptions?: IBMMQConnectionProfile['connectionParams']['tlsOptions']): string {
        // This is a simplified implementation
        // In a real implementation, you would set up the TLS options based on the provided parameters
        return '*TLS12';
    }

    /**
     * Log a message to the output channel
     */
    private log(message: string, isError: boolean = false): void {
        const timestamp = new Date().toISOString();
        const logMessage = `[${timestamp}] ${message}`;

        this.outputChannel.appendLine(logMessage);

        if (isError) {
            console.error(logMessage);
        } else {
            console.log(logMessage);
        }
    }

    /**
     * Parse IBM MQ message timestamp (PutDate and PutTime) into a JavaScript Date object
     * @param putDate The PutDate from the message descriptor (format: YYYYMMDD)
     * @param putTime The PutTime from the message descriptor (format: HHMMSSTH)
     * @returns A JavaScript Date object or undefined if the date/time is invalid
     */
    private parseMessageTimestamp(putDate?: string, putTime?: string): Date | undefined {
        try {
            if (!putDate || !putTime) {
                return undefined;
            }

            // Validate date format (YYYYMMDD)
            if (!/^\d{8}$/.test(putDate)) {
                return undefined;
            }

            // Validate time format (HHMMSSTH)
            if (!/^\d{8}$/.test(putTime)) {
                return undefined;
            }

            // Extract date components
            const year = parseInt(putDate.substring(0, 4), 10);
            const month = parseInt(putDate.substring(4, 6), 10) - 1; // JavaScript months are 0-based
            const day = parseInt(putDate.substring(6, 8), 10);

            // Extract time components
            const hour = parseInt(putTime.substring(0, 2), 10);
            const minute = parseInt(putTime.substring(2, 4), 10);
            const second = parseInt(putTime.substring(4, 6), 10);
            const hundredths = parseInt(putTime.substring(6, 8), 10);

            // Validate date components
            if (year < 1900 || year > 2100 || month < 0 || month > 11 || day < 1 || day > 31) {
                return undefined;
            }

            // Validate time components
            if (hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59 || hundredths < 0 || hundredths > 99) {
                return undefined;
            }

            // Create date object
            const date = new Date(year, month, day, hour, minute, second, hundredths * 10);

            // Check if the date is valid
            if (isNaN(date.getTime())) {
                return undefined;
            }

            return date;
        } catch (error) {
            this.log(`Error parsing message timestamp: ${(error as Error).message}`, true);
            return undefined;
        }
    }

    /**
     * Execute a PCF command
     * @param pcfCmd PCF command to execute
     * @param hObj Queue object handle for the command queue
     * @returns Promise that resolves with the PCF response
     */
    private async executePCFCommand(pcfCmd: PCFCommand, hObj: mq.MQObject | null): Promise<PCFResponse[]> {
        if (!hObj) {
            throw new Error('Invalid queue object handle');
        }
        return new Promise<PCFResponse[]>((resolve, reject) => {
            try {
                // Create a message descriptor for the PCF command
                const mqmd = new mq.MQMD();
                mqmd.Format = mq.MQC.MQFMT_PCF;
                mqmd.MsgType = mq.MQC.MQMT_REQUEST;
                mqmd.ReplyToQ = 'SYSTEM.DEFAULT.MODEL.QUEUE';

                // Create a put message options structure
                const pmo = new mq.MQPMO();
                pmo.Options = mq.MQC.MQPMO_NO_SYNCPOINT |
                             mq.MQC.MQPMO_NEW_MSG_ID |
                             mq.MQC.MQPMO_NEW_CORREL_ID;

                // Convert PCF command to buffer
                const buffer = this.pcfCommandToBuffer(pcfCmd);

                // Put the PCF command to the command queue
                mq.Put(hObj, mqmd, pmo, buffer, (putErr) => {
                    if (putErr) {
                        reject(new Error(`Error putting PCF command: ${putErr.message}`));
                        return;
                    }

                    // Open a temporary dynamic queue for the response
                    const modelQueueName = 'SYSTEM.DEFAULT.MODEL.QUEUE';
                    const dynamicQueueOd = new mq.MQOD();
                    dynamicQueueOd.ObjectName = modelQueueName;
                    dynamicQueueOd.DynamicQName = 'PCFRESPONSE.*';

                    const openOptionsForGet = mq.MQC.MQOO_INPUT_AS_Q_DEF;

                    mq.Open(this.connectionHandle!, dynamicQueueOd, openOptionsForGet, (openErr, responseQObj) => {
                        if (openErr) {
                            reject(new Error(`Error opening response queue: ${openErr.message}`));
                            return;
                        }

                        // Create a message descriptor for the response
                        const responseMqmd = new mq.MQMD();
                        responseMqmd.CorrelId = mqmd.MsgId;

                        // Create a get message options structure
                        const gmo = new mq.MQGMO();
                        gmo.Options = mq.MQC.MQGMO_WAIT | mq.MQC.MQGMO_FAIL_IF_QUIESCING;
                        gmo.MatchOptions = mq.MQC.MQMO_MATCH_CORREL_ID;
                        gmo.WaitInterval = 10000; // 10 seconds

                        // Get the response
                        mq.Get(responseQObj, responseMqmd, gmo, (getErr, responseBuffer: Buffer) => {
                            // Close the response queue
                            mq.Close(responseQObj, 0, () => {
                                if (getErr) {
                                    reject(new Error(`Error getting PCF response: ${getErr.message}`));
                                } else {
                                    // Parse the PCF response
                                    try {
                                        const responses = this.parsePCFResponse(responseBuffer);
                                        resolve(responses);
                                    } catch (parseErr) {
                                        reject(new Error(`Error parsing PCF response: ${parseErr}`));
                                    }
                                }
                            });
                        });
                    });
                });
            } catch (err) {
                reject(new Error(`Error executing PCF command: ${err}`));
            }
        });
    }

    /**
     * Convert a PCF command to a buffer
     * @param pcfCmd PCF command object
     * @returns Buffer containing the PCF command
     */
    private pcfCommandToBuffer(pcfCmd: PCFCommand): Buffer {
        // This is a simplified implementation
        // In a real implementation, you would need to properly format the PCF command
        // according to the PCF specification

        // Calculate the size of the buffer needed
        let bufferSize = 36; // MQCFH header size

        // Add space for each parameter
        for (const param of pcfCmd.Parameters) {
            if (param.String) {
                // String parameter (MQCFST)
                bufferSize += 16 + Math.ceil(param.String.length / 4) * 4;
            } else if (param.Value !== undefined) {
                // Integer parameter (MQCFIN)
                bufferSize += 16;
            } else if (param.Values) {
                // Integer list parameter (MQCFIL)
                bufferSize += 16 + param.Values.length * 4;
            } else if (param.Strings) {
                // String list parameter (MQCFSL)
                let stringsSize = 0;
                for (const str of param.Strings) {
                    stringsSize += Math.ceil(str.length / 4) * 4;
                }
                bufferSize += 16 + stringsSize;
            }
        }

        // Create the buffer
        const buffer = Buffer.alloc(bufferSize);

        // Write the MQCFH header
        buffer.writeInt32BE(mq.MQC.MQCFT_COMMAND, 0); // Type
        buffer.writeInt32BE(bufferSize, 4); // StrucLength
        buffer.writeInt32BE(mq.MQC.MQCFH_VERSION_1, 8); // Version
        buffer.writeInt32BE(pcfCmd.Command, 12); // Command
        buffer.writeInt32BE(0, 16); // MsgSeqNumber
        buffer.writeInt32BE(1, 20); // Control
        buffer.writeInt32BE(pcfCmd.Parameters.length, 24); // ParameterCount

        // Write the parameters
        let offset = 36;
        for (const param of pcfCmd.Parameters) {
            if (param.String) {
                // String parameter (MQCFST)
                buffer.writeInt32BE(mq.MQC.MQCFT_STRING, offset); // Type
                buffer.writeInt32BE(16 + Math.ceil(param.String.length / 4) * 4, offset + 4); // StrucLength
                buffer.writeInt32BE(param.Parameter, offset + 8); // Parameter
                buffer.writeInt32BE(param.String.length, offset + 12); // StringLength
                buffer.write(param.String, offset + 16); // String
                offset += 16 + Math.ceil(param.String.length / 4) * 4;
            } else if (param.Value !== undefined) {
                // Integer parameter (MQCFIN)
                buffer.writeInt32BE(mq.MQC.MQCFT_INTEGER, offset); // Type
                buffer.writeInt32BE(16, offset + 4); // StrucLength
                buffer.writeInt32BE(param.Parameter, offset + 8); // Parameter
                buffer.writeInt32BE(param.Value, offset + 12); // Value
                offset += 16;
            }
            // Add other parameter types as needed
        }

        return buffer;
    }

    /**
     * Parse a PCF response buffer
     * @param buffer Buffer containing the PCF response
     * @returns Array of PCF response objects
     */
    private parsePCFResponse(buffer: Buffer): PCFResponse[] {
        // This is a simplified implementation
        // In a real implementation, you would need to properly parse the PCF response
        // according to the PCF specification

        const responses: any[] = [];

        // Read the MQCFH header
        const type = buffer.readInt32BE(0);
        const strucLength = buffer.readInt32BE(4);
        const version = buffer.readInt32BE(8);
        const command = buffer.readInt32BE(12);
        const msgSeqNumber = buffer.readInt32BE(16);
        const control = buffer.readInt32BE(20);
        const parameterCount = buffer.readInt32BE(24);
        const compCode = buffer.readInt32BE(28);
        const reason = buffer.readInt32BE(32);

        // Create a response object
        const response: any = {
            type,
            strucLength,
            version,
            command,
            msgSeqNumber,
            control,
            parameterCount,
            compCode,
            reason,
            parameters: []
        };

        // Read the parameters
        let offset = 36;
        for (let i = 0; i < parameterCount; i++) {
            const paramType = buffer.readInt32BE(offset);
            const paramStrucLength = buffer.readInt32BE(offset + 4);
            const paramParameter = buffer.readInt32BE(offset + 8);

            if (paramType === mq.MQC.MQCFT_STRING) {
                // String parameter (MQCFST)
                const stringLength = buffer.readInt32BE(offset + 12);
                const string = buffer.toString('utf8', offset + 16, offset + 16 + stringLength);
                response.parameters.push({
                    Type: paramType,
                    StrucLength: paramStrucLength,
                    Parameter: paramParameter,
                    StringLength: stringLength,
                    String: string
                });
            } else if (paramType === mq.MQC.MQCFT_INTEGER) {
                // Integer parameter (MQCFIN)
                const value = buffer.readInt32BE(offset + 12);
                response.parameters.push({
                    Type: paramType,
                    StrucLength: paramStrucLength,
                    Parameter: paramParameter,
                    Value: value
                });
            } else if (paramType === mq.MQC.MQCFT_STRING_LIST) {
                // String list parameter (MQCFSL)
                const count = buffer.readInt32BE(offset + 12);
                const stringLength = buffer.readInt32BE(offset + 16);
                const strings: string[] = [];

                let stringOffset = offset + 20;
                for (let j = 0; j < count; j++) {
                    const string = buffer.toString('utf8', stringOffset, stringOffset + stringLength);
                    strings.push(string.trim());
                    stringOffset += stringLength;
                }

                response.parameters.push({
                    Type: paramType,
                    StrucLength: paramStrucLength,
                    Parameter: paramParameter,
                    Count: count,
                    StringLength: stringLength,
                    Strings: strings
                });
            } else if (paramType === mq.MQC.MQCFT_INTEGER_LIST) {
                // Integer list parameter (MQCFIL)
                const count = buffer.readInt32BE(offset + 12);
                const values: number[] = [];

                let valueOffset = offset + 16;
                for (let j = 0; j < count; j++) {
                    const value = buffer.readInt32BE(valueOffset);
                    values.push(value);
                    valueOffset += 4;
                }

                response.parameters.push({
                    Type: paramType,
                    StrucLength: paramStrucLength,
                    Parameter: paramParameter,
                    Count: count,
                    Values: values
                });
            }

            offset += paramStrucLength;
        }

        responses.push(response);
        return responses;
    }
}
