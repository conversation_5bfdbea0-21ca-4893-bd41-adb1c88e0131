# Exclude everything by default
**/*

# Only include the necessary files
!dist/**
!package.json
!README.md
!LICENSE
!images/**

# Include only necessary files from node_modules
!node_modules/node-addon-api/**
!node_modules/node-gyp/**
!node_modules/node-gyp-build/**
!node_modules/prebuildify/**
!node_modules/proxy-agent/**
!node_modules/env-paths/**
!node_modules/exponential-backoff/**
!node_modules/graceful-fs/**
!node_modules/make-fetch-happen/**
!node_modules/nopt/**
!node_modules/proc-log/**
!node_modules/semver/**
!node_modules/tar/**
!node_modules/tinyglobby/**
!node_modules/which/**
!node_modules/@npmcli/agent/**
!node_modules/cacache/**
!node_modules/http-cache-semantics/**
!node_modules/minipass/**
!node_modules/minipass-fetch/**
!node_modules/minipass-flush/**
!node_modules/minipass-pipeline/**
!node_modules/negotiator/**
!node_modules/promise-retry/**
!node_modules/ssri/**
!node_modules/agent-base/**
!node_modules/http-proxy-agent/**
!node_modules/https-proxy-agent/**
!node_modules/lru-cache/**
!node_modules/socks-proxy-agent/**
!node_modules/debug/**
!node_modules/ms/**
!node_modules/socks/**
!node_modules/ip-address/**
!node_modules/smart-buffer/**
!node_modules/jsbn/**
!node_modules/sprintf-js/**
!node_modules/@npmcli/fs/**
!node_modules/fs-minipass/**
!node_modules/glob/**
!node_modules/minipass-collect/**
!node_modules/p-map/**
!node_modules/unique-filename/**
!node_modules/foreground-child/**
!node_modules/jackspeak/**
!node_modules/minimatch/**
!node_modules/package-json-from-dist/**
!node_modules/path-scurry/**
!node_modules/cross-spawn/**
!node_modules/signal-exit/**
!node_modules/path-key/**
!node_modules/shebang-command/**
!node_modules/shebang-regex/**
!node_modules/isexe/**
!node_modules/@isaacs/cliui/**
!node_modules/brace-expansion/**
!node_modules/balanced-match/**
!node_modules/@isaacs/fs-minipass/**
!node_modules/chownr/**
!node_modules/minizlib/**
!node_modules/mkdirp/**
!node_modules/yallist/**
!node_modules/unique-slug/**
!node_modules/imurmurhash/**
!node_modules/minipass-sized/**
!node_modules/err-code/**
!node_modules/retry/**
!node_modules/abbrev/**
!node_modules/minimist/**
!node_modules/mkdirp-classic/**
!node_modules/node-abi/**
!node_modules/npm-run-path/**
!node_modules/pump/**
!node_modules/tar-fs/**
!node_modules/end-of-stream/**
!node_modules/once/**
!node_modules/wrappy/**
!node_modules/tar-stream/**
!node_modules/bl/**
!node_modules/fs-constants/**
!node_modules/inherits/**
!node_modules/readable-stream/**
!node_modules/buffer/**
!node_modules/base64-js/**
!node_modules/ieee754/**
!node_modules/core-util-is/**
!node_modules/isarray/**
!node_modules/process-nextick-args/**
!node_modules/safe-buffer/**
!node_modules/string_decoder/**
!node_modules/util-deprecate/**
!node_modules/pac-proxy-agent/**
!node_modules/proxy-from-env/**
!node_modules/@tootallnate/quickjs-emscripten/**
!node_modules/get-uri/**
!node_modules/pac-resolver/**
!node_modules/basic-ftp/**
!node_modules/data-uri-to-buffer/**
!node_modules/degenerator/**
!node_modules/netmask/**
!node_modules/ast-types/**
!node_modules/escodegen/**
!node_modules/esprima/**
!node_modules/tslib/**
!node_modules/estraverse/**
!node_modules/esutils/**
!node_modules/ibmmq/**
!node_modules/buffer-more-ints/**
!node_modules/url-parse/**
!node_modules/querystringify/**
!node_modules/requires-port/**
!node_modules/amqplib/**
!node_modules/kafkajs/**
!node_modules/qs/**
!node_modules/stompit/**
!node_modules/@azure/abort-controller/**
!node_modules/@azure/core-amqp/**
!node_modules/@azure/core-auth/**
!node_modules/@azure/core-client/**
!node_modules/@azure/core-rest-pipeline/**
!node_modules/@azure/core-tracing/**
!node_modules/@azure/core-paging/**
!node_modules/@azure/core-util/**
!node_modules/@azure/core-xml/**
!node_modules/@azure/logger/**
!node_modules/@types/is-buffer/**
!node_modules/is-buffer/**
!node_modules/jssha/**
!node_modules/long/**
!node_modules/process/**
!node_modules/rhea-promise/**
!node_modules/events/**
!node_modules/rhea/**
!node_modules/@typespec/ts-http-runtime/**
!node_modules/fast-xml-parser/**
!node_modules/@types/node/**
!node_modules/undici-types/**
!node_modules/@azure/service-bus/**
!node_modules/@azure/msal-browser/**
!node_modules/@azure/msal-node/**
!node_modules/open/**
!node_modules/@azure/msal-common/**
!node_modules/jsonwebtoken/**
!node_modules/uuid/**
!node_modules/jws/**
!node_modules/lodash.includes/**
!node_modules/lodash.isboolean/**
!node_modules/lodash.isinteger/**
!node_modules/lodash.isnumber/**
!node_modules/lodash.isplainobject/**
!node_modules/lodash.isstring/**
!node_modules/lodash.once/**
!node_modules/jwa/**
!node_modules/buffer-equal-constant-time/**
!node_modules/ecdsa-sig-formatter/**
!node_modules/@azure/identity/**
!node_modules/@aws-crypto/sha256-browser/**
!node_modules/@aws-crypto/sha256-js/**
!node_modules/@aws-sdk/core/**
!node_modules/@aws-sdk/credential-provider-node/**
!node_modules/@aws-sdk/middleware-host-header/**
!node_modules/@aws-sdk/middleware-logger/**
!node_modules/@aws-sdk/middleware-recursion-detection/**
!node_modules/@aws-sdk/middleware-sdk-sqs/**
!node_modules/@aws-sdk/middleware-user-agent/**
!node_modules/@aws-sdk/region-config-resolver/**
!node_modules/@aws-sdk/types/**
!node_modules/@aws-sdk/util-endpoints/**
!node_modules/@aws-sdk/util-user-agent-browser/**
!node_modules/@aws-sdk/util-user-agent-node/**
!node_modules/@smithy/config-resolver/**
!node_modules/@smithy/core/**
!node_modules/@smithy/fetch-http-handler/**
!node_modules/@smithy/hash-node/**
!node_modules/@smithy/invalid-dependency/**
!node_modules/@smithy/md5-js/**
!node_modules/@smithy/middleware-content-length/**
!node_modules/@smithy/middleware-endpoint/**
!node_modules/@smithy/middleware-retry/**
!node_modules/@smithy/middleware-serde/**
!node_modules/@smithy/middleware-stack/**
!node_modules/@smithy/node-config-provider/**
!node_modules/@smithy/node-http-handler/**
!node_modules/@smithy/protocol-http/**
!node_modules/@smithy/smithy-client/**
!node_modules/@smithy/types/**
!node_modules/@smithy/url-parser/**
!node_modules/@smithy/util-base64/**
!node_modules/@smithy/util-body-length-browser/**
!node_modules/@smithy/util-body-length-node/**
!node_modules/@smithy/util-defaults-mode-browser/**
!node_modules/@smithy/util-defaults-mode-node/**
!node_modules/@smithy/util-endpoints/**
!node_modules/@smithy/util-middleware/**
!node_modules/@smithy/util-retry/**
!node_modules/@smithy/util-utf8/**
!node_modules/@aws-crypto/supports-web-crypto/**
!node_modules/@aws-crypto/util/**
!node_modules/@aws-sdk/util-locate-window/**
!node_modules/@smithy/util-buffer-from/**
!node_modules/@smithy/is-array-buffer/**
!node_modules/@smithy/property-provider/**
!node_modules/@smithy/signature-v4/**
!node_modules/@smithy/util-stream/**
!node_modules/@smithy/util-hex-encoding/**
!node_modules/@smithy/querystring-builder/**
!node_modules/@smithy/util-uri-escape/**
!node_modules/@smithy/abort-controller/**
!node_modules/@smithy/shared-ini-file-loader/**
!node_modules/@smithy/querystring-parser/**
!node_modules/@aws-sdk/credential-provider-env/**
!node_modules/@aws-sdk/credential-provider-http/**
!node_modules/@aws-sdk/credential-provider-ini/**
!node_modules/@aws-sdk/credential-provider-process/**
!node_modules/@aws-sdk/credential-provider-sso/**
!node_modules/@aws-sdk/credential-provider-web-identity/**
!node_modules/@smithy/credential-provider-imds/**
!node_modules/@aws-sdk/nested-clients/**
!node_modules/@aws-sdk/client-sso/**
!node_modules/@aws-sdk/token-providers/**
!node_modules/@smithy/util-config-provider/**
!node_modules/bowser/**
!node_modules/@smithy/service-error-classification/**
!node_modules/@aws-sdk/client-sqs/**

!node_modules/amqplib/**
!node_modules/kafkajs/**
!node_modules/stompit/**
!node_modules/@azure/service-bus/**
!node_modules/@azure/identity/**
!node_modules/@aws-sdk/client-sqs/**
!node_modules/uuid/**
