To retrieve a list of available queues, you'd send an MQCMD_INQUIRE_Q_NAMES PCF command. The Queue Manager then responds with one or more PCF messages containing the queue names.
Here's a conceptual breakdown of what a PCF message for this would look like and what parameters are involved. Note that you don't usually construct these raw byte messages yourself when using client libraries like ibmmq for Node.js. The library provides higher-level functions that create and parse these PCF messages for you.
Conceptual PCF Command: MQCMD_INQUIRE_Q_NAMES
PCF Header (MQCFH):
Version: PCF version (e.g., MQCFH_VERSION_3)
Type: MQCFT_COMMAND
Command: MQCMD_INQUIRE_Q_NAMES
MsgSeqNumber: Usually 1 for a single command.
Control: MQCFC_LAST (if it's the only/last command in the message)
ParameterCount: Number of PCF parameters that follow.
PCF Parameters (attached to the header):
MQCA_Q_NAME (Queue Name Filter):
Type: MQCFST (PCF String)
Parameter ID: MQCA_Q_NAME
Value: A string pattern to filter queue names.
* or MQSQ_ALL: To get all queues.
SYSTEM.*: To get all queues starting with "SYSTEM.".
DEV.TEST.*: To get queues starting with "DEV.TEST.".
MY.SPECIFIC.QUEUE: To inquire about a single specific queue (though MQCMD_INQUIRE_Q is often used for single queue details).
MQIA_Q_TYPE (Queue Type Filter - Optional):
Type: MQCFIN (PCF Integer)
Parameter ID: MQIA_Q_TYPE
Value:
MQQT_ALL: All queue types.
MQQT_LOCAL: Only local queues.
MQQT_ALIAS: Only alias queues.
MQQT_REMOTE: Only remote queue definitions.
MQQT_MODEL: Only model queues.
MQCFOP_COMMAND_SCOPE (Command Scope - Optional, for QSG on z/OS):
Type: MQCFIN (PCF Integer)
Parameter ID: MQCFOP_COMMAND_SCOPE
Value: Specifies the scope of the command in a Queue Sharing Group (e.g., MQQSCO_ALL or a specific QMGR name). Usually not needed for distributed platforms unless specifically targeting a QSG member.
MQIACF_Q_ATTRS (Queue Attributes - Optional, if you want specific attributes alongside names):
While MQCMD_INQUIRE_Q_NAMES primarily returns names, you can request some attributes. However, if you need many attributes, MQCMD_INQUARE_Q is more common for individual queues or a filtered set.
Type: MQCFIL (PCF Integer List)
Parameter ID: MQIACF_Q_ATTRS
Values: An array of MQCA_* or MQIA_* constants representing the attributes you want (e.g., MQCA_Q_DESC, MQIA_CURRENT_Q_DEPTH). Using this makes the command behave more like MQCMD_INQUIRE_Q but filtered by name.
How Client Libraries (like ibmmq for Node.js) Handle This:
The ibmmq library (and similar Java/C/.NET clients) typically abstracts this away. You wouldn't manually build the byte stream. Instead, you'd use a function or method that might look conceptually like this (this is a simplified conceptual example, not exact ibmmq API):
// --- THIS IS CONCEPTUAL PSEUDOCODE ---
// Not the actual ibmmq API for PCF, which is more involved
// but illustrates the idea.

// hConn is the connection handle to the Queue Manager

// Prepare arguments for the inquire operation
const inquireArgs = {
    qName: 'DEV.*', // Filter for queues starting with DEV.
    qType: MQC.MQQT_LOCAL, // Only local queues
    // You might specify which attributes you want if the function supports it
};

// The library would internally construct the MQCMD_INQUIRE_Q_NAMES PCF message
// send it to the SYSTEM.ADMIN.COMMAND.QUEUE (or similar),
// set up a dynamic reply queue, wait for responses, and parse them.

mq.inquireQueueNames(hConn, inquireArgs, (err, queueNamesArray, pcfResponses) => {
    if (err) {
        console.error("Error inquiring queue names:", err);
        return;
    }
    console.log("Found Queues:", queueNamesArray);
    // pcfResponses might contain the raw PCF response messages if needed for more detail
});
// --- END OF CONCEPTUAL PSEUDOCODE ---
Use code with caution.
JavaScript
Actual ibmmq Library Usage (More Complex - Using MQPUT to Command Queue & MQGET from Reply Queue):
For direct PCF with the ibmmq Node.js library, you'd generally:
Construct the PCF message buffer: This involves creating Buffer objects and carefully packing the MQCFH and various MQCFST, MQCFIN, etc., structures according to their C-struct layouts. This is tedious and error-prone. The ibmmq library has some helper classes like PCFMessage and parameter classes (e.g., MQCFST, MQCFIN) that make this somewhat easier than raw buffer manipulation, but it's still low-level.
Open the command server input queue: Usually SYSTEM.ADMIN.COMMAND.QUEUE or SYSTEM.COMMAND.INPUT.QUEUE.
Define a Reply-To Queue: This is often a dynamic queue created from SYSTEM.DEFAULT.MODEL.QUEUE.
Put the PCF message: Use mq.Put with the PCF message buffer, specifying the reply-to queue and manager in the MQMD.
Get the response(s): Use mq.Get on your reply queue. You might get multiple response messages if many queues match.
Parse the PCF response messages: Extract the queue names (which will be in MQCA_Q_NAME parameters within MQCFSL - String List - structures in the response) and other data.
Example using the ibmmq library's PCF helpers (Simplified):
The ibmmq library includes a PCFMessage class and parameter classes. Here's a very high-level idea of how you might use it to build the command (the actual implementation is more involved with error handling, looping for all responses, etc.):
// --- HIGHLY SIMPLIFIED EXAMPLE FRAGMENT ---
const mq = require('ibmmq');
const MQC = mq.MQC;
const pcf = mq.PCF; // Access PCF helper classes

// ... (after connecting to QM - hConn) ...

async function listQueues(hConn, qNameFilter = '*', qTypeFilter = MQC.MQQT_ALL) {
    const agent = new pcf.MQAI(); // MQAI is often used for PCF interactions

    // Create the PCF Inquire Queue Names command message
    const pcfCmd = new pcf.PCFMessage(null, MQC.MQCMD_INQUIRE_Q_NAMES, 0, false);
    pcfCmd.addParameter(MQC.MQCA_Q_NAME, qNameFilter);
    pcfCmd.addParameter(MQC.MQIA_Q_TYPE, qTypeFilter);
    // Optionally add MQIACF_Q_ATTRS if you want specific attributes included in the response

    try {
        // The agent.send() method (or similar underlying mechanism) would handle:
        // - Opening SYSTEM.ADMIN.COMMAND.QUEUE
        // - Creating/specifying a reply queue
        // - Putting the command
        // - Getting responses
        // - Parsing responses
        const pcfResponses = await agent.send(hConn, pcfCmd.getBuffer()); // Conceptual send

        const queueNames = [];
        for (const response of pcfResponses) {
            if (response.isPCF()) {
                const pcfResponseMsg = new pcf.PCFMessage(response.getMessage());
                if (pcfResponseMsg.getCommand() === MQC.MQCMD_INQUIRE_Q_NAMES) {
                    const qNameList = pcfResponseMsg.getParameterValue(MQC.MQCA_Q_NAME_LIST); // Or similar constant for list of names
                    if (qNameList && Array.isArray(qNameList)) {
                        qNameList.forEach(name => queueNames.push(name.trim())); // Names might have padding
                    }
                }
            }
        }
        return queueNames;
    } catch (err) {
        console.error("PCF Error:", err);
        // Extract MQCC and MQRC from the error if it's an MQ Error
        if (err.mqcc !== undefined && err.mqrc !== undefined) {
            console.error(`PCF MQCC: ${err.mqcc_name} [${err.mqcc}], MQRC: ${err.mqrc_name} [${err.mqrc}]`);
        }
        throw err;
    }
}

// Usage:
// listQueues(hConn, 'DEV.*', MQC.MQQT_LOCAL)
//   .then(names => console.log("Queues:", names))
//   .catch(e => console.error("Failed to list queues"));
// --- END OF HIGHLY SIMPLIFIED EXAMPLE FRAGMENT ---
Use code with caution.
JavaScript
Key takeaway for your MQExplorer extension:
Your IBMMQProvider.listQueues() method will need to implement the logic to:
Construct an MQCMD_INQUIRE_Q_NAMES PCF message (using ibmmq library's PCF helpers is strongly recommended over raw buffer manipulation).
Send this message to the QM's command queue.
Receive and parse the PCF response(s) to extract the list of queue names.
Handle potential errors, including the MQRC_NOT_AUTHORIZED (2035) if permissions are insufficient for these PCF operations.
This is one of the more complex interactions with MQ, but it's fundamental for a browser tool. The ibmmq samples and documentation for PCF will be your best friend here. Look for examples related to PCFMessage or administrative commands.